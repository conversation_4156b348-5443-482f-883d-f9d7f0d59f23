# SPDX-FileCopyrightText: Terrafloww Labs, 2025

[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "terrafloww"
version = "0.1.0"
description = "Lightweight Python SDK for the Terrafloww Geospatial AI Platform"
readme = "README.md"
license = {text = "Terrafloww Labs Proprietary"}
requires-python = ">=3.10"
authors = [
    {name = "Terrafloww Labs", email = "<EMAIL>"}
]
keywords = ["geospatial", "satellite", "imagery", "ai", "ml", "remote-sensing"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "Topic :: Scientific/Engineering :: GIS",
    "Topic :: Scientific/Engineering :: Image Processing",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]

dependencies = [
    "grpcio>=1.50.0",
    "protobuf>=4.0.0",
    "pyarrow[flight]>=15.0.0",
    "geopandas>=0.12.0",
    "shapely>=2.0.0",
    "numpy>=1.20.0",
    "pandas>=1.5.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0",
    "pytest-asyncio>=0.21",
    "black>=23.0",
    "ruff>=0.1.0",
    "mypy>=1.0",
]

[project.urls]
Homepage = "https://terrafloww.com"
Documentation = "https://docs.terrafloww.com"
Repository = "https://github.com/terrafloww/terrafloww-sdk"
Issues = "https://github.com/terrafloww/terrafloww-sdk/issues"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
terrafloww = ["py.typed"]

# Development tools configuration
[tool.black]
line-length = 100
target-version = ['py310']

[tool.ruff]
line-length = 100
target-version = "py310"
select = ["E", "F", "W", "I", "N", "UP", "B", "A", "C4", "T20"]

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
