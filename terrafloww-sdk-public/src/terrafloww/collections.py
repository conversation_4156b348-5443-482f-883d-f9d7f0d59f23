# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
GeoImageCollection - Core data structure for the Terrafloww SDK.

This module provides the main GeoImageCollection class that represents
lazy computation graphs for geospatial data processing.
"""

import logging
import asyncio
import os
from typing import Any, Dict, List, Optional
from uuid import uuid4

import pyarrow as pa
import grpc.aio as grpc_async
from pyarrow import flight as fl
from google.protobuf.struct_pb2 import Struct

# Import generated protobuf/gRPC code
from .processing_engine.v1 import processing_engine_pb2
from .processing_engine.v1 import processing_engine_pb2_grpc

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Default processing engine targets from environment variables
DEFAULT_GRPC_TARGET = os.environ.get("TFW_PROCESSING_GRPC_TARGET", "localhost:50051")
DEFAULT_FLIGHT_TARGET = os.environ.get("TFW_PROCESSING_FLIGHT_TARGET", "grpc+tcp://localhost:50052")


class GeoImageCollection:
    """
    Represents a lazy computation graph for geospatial data processing.
    
    This class builds up a pipeline of operations (load, apply, head) that are
    executed server-side when compute() is called. No heavy processing happens
    locally - all computation is done on the Terrafloww Platform.
    """
    
    def __init__(
        self,
        operation_type: str,
        operation_details: Dict[str, Any],
        parent_node: Optional['GeoImageCollection'] = None,
        window_limit: Optional[int] = None,
        node_id: Optional[str] = None,
        processing_engine_grpc_target: Optional[str] = None,
        processing_engine_flight_target: Optional[str] = None
    ):
        self._operation_type = operation_type
        self._operation_details = operation_details
        self._parent_node = parent_node
        self._window_limit = window_limit
        self._node_id = node_id or f"{operation_type}_{uuid4().hex[:8]}"
        
        # Set processing engine targets, inheriting from parent or using defaults
        self._grpc_target = (
            processing_engine_grpc_target or 
            (parent_node._grpc_target if parent_node else None) or 
            DEFAULT_GRPC_TARGET
        )
        self._flight_target = (
            processing_engine_flight_target or 
            (parent_node._flight_target if parent_node else None) or 
            DEFAULT_FLIGHT_TARGET
        )
        
        # Build pipeline steps from load
        self._pipeline_steps_from_load: List[Dict[str, Any]] = []
        curr = self
        while curr is not None:
            step_details = {
                "operation_type": curr._operation_type,
                "operation_details": curr._operation_details,
                "node_id": curr._node_id,
            }
            self._pipeline_steps_from_load.insert(0, step_details)
            curr = curr._parent_node
            
        logger.debug(f"Initialized GeoImageCollection Node: {self._node_id} (Type: {self._operation_type})")

    def apply(self, function_id: str, parameters: Optional[Dict[str, Any]] = None) -> 'GeoImageCollection':
        """
        Apply a server-side operation to the GeoImageCollection.
        
        Args:
            function_id: ID of the server-side function (e.g., "ndvi", "ndwi")
            parameters: Dictionary of parameters for the function
            
        Returns:
            A new GeoImageCollection with the operation applied
            
        Examples:
            >>> ndvi = collection.apply("ndvi", {"red_band": "B4", "nir_band": "B8"})
            >>> water = collection.apply("ndwi", {"green_band": "B3", "nir_band": "B8"})
        """
        logger.info(f"Adding APPLY_FUNC step with function_id: {function_id}")
        
        operation_details = {
            "function_id": function_id,
            "kwargs": parameters or {},
        }
        
        return GeoImageCollection(
            operation_type="APPLY_FUNC",
            operation_details=operation_details,
            parent_node=self,
            window_limit=self._window_limit,
            processing_engine_grpc_target=self._grpc_target,
            processing_engine_flight_target=self._flight_target
        )

    def head(self, n_windows: int = 5) -> 'GeoImageCollection':
        """
        Limit the number of unique spatial windows in the collection.
        
        Args:
            n_windows: Number of unique spatial windows to keep
            
        Returns:
            A new GeoImageCollection with the head limit applied
        """
        if not isinstance(n_windows, int) or n_windows <= 0:
            raise ValueError("n_windows must be positive.")
            
        logger.info(f"Adding HEAD_LIMIT step: n={n_windows}")
        effective_limit = min(self._window_limit, n_windows) if self._window_limit is not None else n_windows
        
        operation_details = {
            "n": n_windows,
            "spatial_window_limit": effective_limit
        }
        
        return GeoImageCollection(
            operation_type="HEAD_LIMIT",
            operation_details=operation_details,
            parent_node=self,
            window_limit=effective_limit,
            processing_engine_grpc_target=self._grpc_target,
            processing_engine_flight_target=self._flight_target
        )

    def compute(self) -> pa.Table:
        """
        Execute the computation graph and return results as a PyArrow Table.
        
        This method sends the workflow to the Terrafloww Platform for execution
        and retrieves the results via Arrow Flight.
        
        Returns:
            PyArrow Table containing the processed results
        """
        return asyncio.run(self._compute_async())

    async def _compute_async(self) -> pa.Table:
        """Execute the graph asynchronously and return a PyArrow Table."""
        job_id = f"job_{uuid4().hex}"
        logger.info(f"Executing workflow with job_id: {job_id}")
        
        try:
            # Build the workflow plan
            plan = self._build_workflow_plan()
            
            # Create the gRPC request
            request = processing_engine_pb2.ExecuteWorkflowRequest(
                job_id=job_id,
                plan=plan,
                execution_mode=processing_engine_pb2.ExecuteWorkflowRequest.ExecutionMode.RAY
            )
            
            # Execute via gRPC
            channel = self._get_grpc_channel()
            stub = processing_engine_pb2_grpc.ProcessingEngineServiceStub(channel)
            
            logger.info(f"Sending ExecuteWorkflow request to {self._grpc_target}")
            response = await stub.ExecuteWorkflow(request)
            logger.info(f"Received ExecuteWorkflow response: {response}")
            
            # Get results via Flight
            flight_ticket = response.flight_ticket
            if not flight_ticket:
                raise ValueError("No Flight ticket received in response")
                
            # Parse flight endpoint
            from urllib.parse import urlparse
            flight_url = urlparse(self._flight_target)
            flight_host = flight_url.hostname
            flight_port = flight_url.port or 50052
            
            # Create Flight client and get results
            client = fl.FlightClient(f"grpc+tcp://{flight_host}:{flight_port}")
            logger.info(f"Connecting to Flight server at {flight_host}:{flight_port}")
            
            ticket = fl.Ticket(flight_ticket.encode('utf-8'))
            
            # Poll for results with retries
            max_retries = 60
            retry_delay = 1.0
            
            for attempt in range(max_retries):
                try:
                    reader = client.do_get(ticket)
                    table = reader.read_all()
                    if table.num_rows > 0:
                        logger.info(f"Received result table with {table.num_rows} rows and {len(table.schema.names)} columns")
                    else:
                        logger.warning("Received empty result set from Flight server")
                    return table
                        
                except fl.FlightUnavailableError:
                    if attempt < max_retries - 1:
                        logger.info(f"Workflow {job_id} still in progress (attempt {attempt + 1}/{max_retries}), retrying in {retry_delay}s")
                        await asyncio.sleep(retry_delay)
                    else:
                        raise TimeoutError(f"Workflow {job_id} did not complete after {max_retries} attempts")
                        
        except Exception as e:
            logger.error(f"Error executing workflow {job_id}: {e}")
            raise

    def _get_grpc_channel(self):
        """Get gRPC channel for communication with processing engine."""
        return grpc_async.insecure_channel(self._grpc_target)

    def _build_workflow_plan(self) -> processing_engine_pb2.WorkflowPlan:
        """Build the workflow plan for execution."""
        plan = processing_engine_pb2.WorkflowPlan()
        has_load_step = False

        for step in self._pipeline_steps_from_load:
            op_type = step["operation_type"]
            
            if op_type == "LOAD":
                if has_load_step:
                    continue  # Only use the first LOAD step
                has_load_step = True
                
                op_details = step["operation_details"]
                plan.load_step.collection = str(op_details.get("collection", ""))
                plan.load_step.bands.extend(str(band) for band in (op_details.get("bands") or []))
                plan.load_step.aoi_wkt = str(op_details.get("aoi_wkt", ""))
                plan.load_step.aoi_crs = str(op_details.get("aoi_crs", ""))
                plan.load_step.datetime_filter = str(op_details.get("datetime", ""))
                plan.load_step.scene_limit = int(op_details.get("limit", 0))
                
                # Handle filters
                filters = op_details.get("filters", {})
                if filters:
                    prop_struct = Struct()
                    string_filters = {k: str(v) for k, v in filters.items()}
                    prop_struct.update(string_filters)
                    plan.load_step.property_filters.CopyFrom(prop_struct)
                    
            elif op_type == "APPLY_FUNC":
                apply_func = plan.apply_steps.add()
                function_id = step["operation_details"].get("function_id", "unknown")
                apply_func.function_id = function_id
                params_dict = step["operation_details"].get("kwargs", {})
                params_struct = Struct()
                if params_dict:
                    params_struct.update(params_dict)
                apply_func.parameters.CopyFrom(params_struct)
                
            elif op_type == "HEAD_LIMIT":
                plan.head_limit = step["operation_details"].get("spatial_window_limit", self._window_limit or 0)

        if not has_load_step:
            raise ValueError("Invalid GeoImageCollection: No LOAD step.")
        
        logger.info(f"Built workflow plan with head_limit={plan.head_limit}")
        return plan
