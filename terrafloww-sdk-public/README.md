# Terrafloww SDK

**Lightweight Python SDK for the Terrafloww Geospatial AI Platform**

[![Python 3.10+](https://img.shields.io/badge/python-3.10+-blue.svg)](https://www.python.org/downloads/)
[![License: Proprietary](https://img.shields.io/badge/License-Terrafloww%20Labs%20Proprietary-red.svg)](LICENSE)

## Overview

The Terrafloww SDK provides a clean, lightweight Python interface for processing satellite and aerial imagery at scale. All heavy processing happens server-side on the Terrafloww Platform.

## Quick Start

### Installation

```bash
pip install terrafloww
```

### Basic Usage

```python
import terrafloww as tfw

# Load satellite imagery
collection = tfw.load(
    "sentinel-2-l2a",
    bands=["B4", "B8"],
    bbox=[-74.0, 40.6, -73.9, 40.8],  # NYC area
    datetime="2024-06-01/2024-06-30"
)

# Apply NDVI calculation
ndvi = collection.apply("ndvi", {
    "red_band": "B4", 
    "nir_band": "B8"
})

# Execute and get results
result = ndvi.head(10).compute()
print(f"Processed {result.num_rows} spatial windows")
```

## Key Features

- **🚀 Lightweight**: No heavy dependencies 
- **☁️ Cloud-Native**: All processing happens server-side
- **🔗 Simple API**: Familiar pandas-like interface
- **📊 Arrow Integration**: Efficient data transfer with PyArrow
- **🌍 Geospatial Ready**: Built-in support for common geospatial operations

## API Reference

### Loading Data

```python
# Load by collection name
collection = tfw.load("sentinel-2-l2a")

# Filter by area of interest
collection = tfw.load(
    "sentinel-2-l2a",
    bbox=[-74.0, 40.6, -73.9, 40.8],  # [west, south, east, north]
    datetime="2024-01-01/2024-12-31"
)

# Select specific bands
collection = tfw.load(
    "sentinel-2-l2a", 
    bands=["B2", "B3", "B4", "B8"]
)
```

### Applying Operations

```python
# Built-in operations
ndvi = collection.apply("ndvi", {"red_band": "B4", "nir_band": "B8"})
ndwi = collection.apply("ndwi", {"green_band": "B3", "nir_band": "B8"})

# Chain operations
result = (collection
    .apply("ndvi", {"red_band": "B4", "nir_band": "B8"})
    .apply("threshold", {"column": "ndvi", "min_value": 0.3})
    .head(100)
    .compute())
```

### Execution

```python
# Limit results
limited = collection.head(50)  # First 50 spatial windows

# Execute computation
result = collection.compute()  # Returns PyArrow Table

# Get results as pandas DataFrame
df = result.to_pandas()
```

## Configuration

### Environment Variables

```bash
# Processing Engine endpoints (for self-hosted deployments)
export TFW_PROCESSING_GRPC_TARGET="your-engine.com:50051"
export TFW_PROCESSING_FLIGHT_TARGET="grpc+tcp://your-engine.com:50052"
```

### Custom Endpoints

```python
# Connect to custom deployment
collection = tfw.load(
    "sentinel-2-l2a",
    processing_engine_grpc_target="your-engine.com:50051",
    processing_engine_flight_target="grpc+tcp://your-engine.com:50052"
)
```

## Available Operations

| Operation | Description | Parameters |
|-----------|-------------|------------|
| `ndvi` | Normalized Difference Vegetation Index | `red_band`, `nir_band` |
| `ndwi` | Normalized Difference Water Index | `green_band`, `nir_band` |
| `evi` | Enhanced Vegetation Index | `red_band`, `nir_band`, `blue_band` |
| `threshold` | Apply threshold filter | `column`, `min_value`, `max_value` |
| `mask` | Apply mask based on condition | `column`, `condition` |

## Examples

### Vegetation Analysis

```python
import terrafloww as tfw

# Load Sentinel-2 data for agricultural area
collection = tfw.load(
    "sentinel-2-l2a",
    bands=["B2", "B3", "B4", "B8"],
    bbox=[-120.5, 35.0, -120.0, 35.5],  # Central Valley, CA
    datetime="2024-06-01/2024-08-31"
)

# Calculate vegetation indices
ndvi = collection.apply("ndvi", {"red_band": "B4", "nir_band": "B8"})
healthy_vegetation = ndvi.apply("threshold", {"column": "ndvi", "min_value": 0.4})

# Get results
result = healthy_vegetation.head(200).compute()
print(f"Found {result.num_rows} healthy vegetation areas")
```

### Water Detection

```python
# Load data for water body analysis
collection = tfw.load(
    "sentinel-2-l2a",
    bands=["B3", "B8"],
    bbox=[-74.2, 40.4, -73.7, 40.9],  # NYC water bodies
    datetime="2024-07-01/2024-07-31"
)

# Calculate water index
ndwi = collection.apply("ndwi", {"green_band": "B3", "nir_band": "B8"})
water_areas = ndwi.apply("threshold", {"column": "ndwi", "min_value": 0.2})

result = water_areas.compute()
```

## Requirements

- Python 3.10+
- Internet connection (for API calls to Terrafloww Platform)

## Support

- **Documentation**: [docs.terrafloww.com](https://docs.terrafloww.com)
- **Issues**: [GitHub Issues](https://github.com/terrafloww/terrafloww-sdk/issues)
- **Contact**: <EMAIL>

## License

Terrafloww Labs Proprietary License. See [LICENSE](LICENSE) for details.
