# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Basic tests for the Terrafloww SDK.
"""

import pytest
import terrafloww as tfw


def test_import():
    """Test that the SDK can be imported."""
    assert hasattr(tfw, 'load')
    assert hasattr(tfw, 'GeoImageCollection')


def test_load_creates_collection():
    """Test that load() creates a GeoImageCollection."""
    collection = tfw.load("test-collection")
    assert isinstance(collection, tfw.GeoImageCollection)


def test_collection_chaining():
    """Test that operations can be chained."""
    collection = tfw.load("test-collection")
    result = collection.apply("ndvi", {"red_band": "B4", "nir_band": "B8"}).head(5)
    assert isinstance(result, tfw.GeoImageCollection)


def test_bbox_parameter():
    """Test that bbox parameter works."""
    collection = tfw.load(
        "sentinel-2-l2a",
        bbox=[-74.0, 40.6, -73.9, 40.8],
        datetime="2024-06-01/2024-06-30"
    )
    assert isinstance(collection, tfw.GeoImageCollection)


# Note: These tests don't actually execute compute() since that requires
# a running Terrafloww Platform. For integration tests, see the main
# internal-platform-v2/sdk/src/tests/ directory.
