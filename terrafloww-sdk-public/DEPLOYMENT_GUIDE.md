# Terrafloww SDK Deployment Guide

## Overview

This document explains how to deploy the lightweight Terrafloww SDK as a separate package that users can install without heavy dependencies.

## Architecture Separation

### ✅ Current Setup (Correct)

**Processing Engine + Ray Workers** (Internal Platform):
- Both containers need internal libraries (this is correct!)
- Processing Engine runs Ray driver code
- Ray Workers execute processing functions
- Both are part of the same processing system

**User-Facing SDK** (This Package):
- Lightweight gRPC client only
- No Ray, GDAL, or internal processing libraries
- Clean user experience: `pip install terrafloww`

## Package Structure

```
terrafloww-sdk-public/
├── src/terrafloww/
│   ├── __init__.py              # Main exports
│   ├── loaders.py               # load() function
│   ├── collections.py           # GeoImageCollection class
│   ├── processing_engine/       # gRPC protobuf definitions
│   └── py.typed                 # Type checking marker
├── tests/                       # Basic unit tests
├── pyproject.toml              # Lightweight dependencies
├── README.md                   # User documentation
└── LICENSE                     # Proprietary license
```

## Dependencies Comparison

### ❌ Old SDK (Heavy)
```toml
dependencies = [
    "tfw-raster-schemas>=0.1.0",     # GDAL dependencies
    "tfw-engine-core>=0.1.0",       # Ray dependencies  
    "tfw-processing-api>=0.1.0",
    "tfw-ray-utils>=0.1.0",
    # ... many heavy dependencies
]
```

### ✅ New SDK (Lightweight)
```toml
dependencies = [
    "grpcio>=1.50.0",
    "protobuf>=4.0.0", 
    "pyarrow[flight]>=15.0.0",
    "geopandas>=0.12.0",
    "shapely>=2.0.0",
    "numpy>=1.20.0",
    "pandas>=1.5.0",
]
```

## User Experience

### Before (Heavy)
```bash
pip install terrafloww-sdk  # Installs Ray, GDAL, etc.
# Takes 5+ minutes, requires system dependencies
```

### After (Lightweight)
```bash
pip install terrafloww  # Only client dependencies
# Takes 30 seconds, no system dependencies
```

## Usage Examples

### Simple API
```python
import terrafloww as tfw

# Load data
collection = tfw.load(
    "sentinel-2-l2a",
    bbox=[-74.0, 40.6, -73.9, 40.8],
    datetime="2024-06-01/2024-06-30"
)

# Apply operations
ndvi = collection.apply("ndvi", {"red_band": "B4", "nir_band": "B8"})

# Execute
result = ndvi.head(10).compute()
```

## Deployment Steps

### 1. Move to Separate Repository
```bash
# Create new repository
git init terrafloww-sdk
cd terrafloww-sdk

# Copy the lightweight SDK
cp -r /path/to/terrafloww-sdk-public/* .

# Set up repository
git add .
git commit -m "Initial lightweight SDK"
```

### 2. Publish to PyPI
```bash
# Build package
python -m build

# Upload to PyPI
python -m twine upload dist/*
```

### 3. Update Documentation
- Point users to `pip install terrafloww`
- Update examples to use lightweight API
- Separate internal development docs

## Testing

### Unit Tests (No Platform Required)
```bash
cd terrafloww-sdk-public
python -m pytest tests/
```

### Integration Tests (Requires Platform)
```bash
# Test with running Processing Engine
python -c "
import terrafloww as tfw
collection = tfw.load('sentinel-2-l2a')
result = collection.apply('ndvi', {...}).head(2).compute()
"
```

## Benefits

1. **🚀 Fast Installation**: No heavy dependencies
2. **📦 Small Package Size**: ~10MB vs ~500MB
3. **🔧 Easy Maintenance**: Clear separation of concerns
4. **📈 Better Adoption**: Lower barrier to entry
5. **🎯 Focused API**: Only user-facing functionality

## Migration Path

1. **Phase 1**: Create lightweight SDK (✅ Done)
2. **Phase 2**: Move to separate repository
3. **Phase 3**: Publish to PyPI as `terrafloww`
4. **Phase 4**: Update documentation and examples
5. **Phase 5**: Deprecate old heavy SDK

## Repository Structure (Future)

```
terrafloww/
├── terrafloww-platform/     # Internal platform (current repo)
│   ├── services/
│   ├── libs/
│   └── sdk/                 # Internal development SDK
└── terrafloww-sdk/          # User-facing SDK (new repo)
    ├── src/terrafloww/
    ├── tests/
    └── docs/
```

This separation provides a clean user experience while maintaining the working internal architecture.
