# Terrafloww SDK Deployment Guide

## Overview

This document explains how to deploy the lightweight Terrafloww SDK as a separate package that users can install without heavy dependencies.

## Architecture Separation

### ✅ Current Setup (Correct)

**Processing Engine + Ray Workers** (Internal Platform):
- Both containers need internal libraries (this is correct!)
- Processing Engine runs Ray driver code
- Ray Workers execute processing functions
- Both are part of the same processing system

**User-Facing SDK** (This Package):
- Lightweight gRPC client only
- No Ray, GDAL, or internal processing libraries
- Clean user experience: `pip install terrafloww`

## Package Structure

```
terrafloww-sdk-public/
├── src/terrafloww/
│   ├── __init__.py              # Main exports
│   ├── loaders.py               # load() function
│   ├── collections.py           # GeoImageCollection class
│   ├── processing_engine/       # gRPC protobuf definitions
│   └── py.typed                 # Type checking marker
├── tests/                       # Basic unit tests
├── pyproject.toml              # Lightweight dependencies
├── README.md                   # User documentation
└── LICENSE                     # Proprietary license
```

## Dependencies Comparison

### ❌ Old SDK (Heavy)
```toml
dependencies = [
    "tfw-raster-schemas>=0.1.0",     # GDAL dependencies
    "tfw-engine-core>=0.1.0",       # Ray dependencies  
    "tfw-processing-api>=0.1.0",
    "tfw-ray-utils>=0.1.0",
    # ... many heavy dependencies
]
```

### ✅ New SDK (Lightweight)
```toml
dependencies = [
    "grpcio>=1.50.0",
    "protobuf>=4.0.0", 
    "pyarrow[flight]>=15.0.0",
    "geopandas>=0.12.0",
    "shapely>=2.0.0",
    "numpy>=1.20.0",
    "pandas>=1.5.0",
]
```

## User Experience

### Before (Heavy)
```bash
pip install terrafloww-sdk  # Installs Ray, GDAL, etc.
# Takes 5+ minutes, requires system dependencies
```

### After (Lightweight)
```bash
pip install terrafloww  # Only client dependencies
# Takes 30 seconds, no system dependencies
```

## Usage Examples

### Simple API
```python
import terrafloww as tfw

# Load data
collection = tfw.load(
    "sentinel-2-l2a",
    bbox=[-74.0, 40.6, -73.9, 40.8],
    datetime="2024-06-01/2024-06-30"
)

# Apply operations
ndvi = collection.apply("ndvi", {"red_band": "B4", "nir_band": "B8"})

# Execute
result = ndvi.head(10).compute()
```

## Deployment Steps

### 1. Move to Separate Repository
```bash
# Create new repository
git init terrafloww-sdk
cd terrafloww-sdk

# Copy the lightweight SDK
cp -r /path/to/terrafloww-sdk-public/* .

# Set up repository
git add .
git commit -m "Initial lightweight SDK"
```

### 2. Publish to PyPI
```bash
# Build package
python -m build

# Upload to PyPI
python -m twine upload dist/*
```

### 3. Update Documentation
- Point users to `pip install terrafloww`
- Update examples to use lightweight API
- Separate internal development docs

## Testing

### Unit Tests (No Platform Required)
```bash
cd terrafloww-sdk-public
python -m pytest tests/
```

### Integration Tests (Requires Platform)
```bash
# Test with running Processing Engine
python -c "
import terrafloww as tfw
collection = tfw.load('sentinel-2-l2a')
result = collection.apply('ndvi', {...}).head(2).compute()
"
```

## Benefits

1. **🚀 Fast Installation**: No heavy dependencies
2. **📦 Small Package Size**: ~10MB vs ~500MB
3. **🔧 Easy Maintenance**: Clear separation of concerns
4. **📈 Better Adoption**: Lower barrier to entry
5. **🎯 Focused API**: Only user-facing functionality

## Migration Path

1. **Phase 1**: Create lightweight SDK (✅ Done)
2. **Phase 2**: Move to separate repository
3. **Phase 3**: Publish to PyPI as `terrafloww`
4. **Phase 4**: Update documentation and examples
5. **Phase 5**: Deprecate old heavy SDK

## Repository Structure (Future)

```
terrafloww/
├── terrafloww-platform/     # Internal platform (current repo)
│   ├── services/
│   ├── libs/
│   └── sdk/                 # Internal development SDK
└── terrafloww-sdk/          # User-facing SDK (new repo)
    ├── src/terrafloww/
    ├── tests/
    └── docs/
```

This separation provides a clean user experience while maintaining the working internal architecture.

## Security Considerations

### 🔒 What We're Exposing vs Protecting

#### **Exposed in Public SDK:**
1. **gRPC Protocol Definitions** (`processing_engine_pb2.py`)
   - Basic message structures (WorkflowPlan, LoadStep, ApplyStep)
   - Function signatures and parameter types
   - **Risk Level: LOW** - These are just API contracts, not implementation

2. **GeoImageCollection Class Structure**
   - Workflow building logic (load → apply → head → compute)
   - Parameter validation and serialization
   - **Risk Level: MEDIUM** - Reveals our API design patterns

3. **Operation Names** (e.g., "ndvi", "ndwi")
   - Function IDs that users can call
   - **Risk Level: LOW** - These are intended to be public

#### **Protected on Platform Side:**
1. **All Processing Logic** - Ray workers, algorithms, optimizations
2. **Data Access Patterns** - How we read from catalogs, chunk data
3. **Infrastructure Details** - Kubernetes configs, scaling logic
4. **Performance Optimizations** - Caching, parallelization strategies

### 🛡️ Reverse Engineering Analysis

#### **What Attackers Could Learn:**
```python
# From protobuf definitions
message WorkflowPlan {
    LoadStep load_step = 1;
    repeated ApplyStep apply_steps = 2;
    int32 head_limit = 3;
}
```
- **Limited Value**: Just shows we have load/apply/head operations
- **No Implementation Details**: Doesn't reveal how we process data

#### **What They Cannot Learn:**
- How NDVI is actually calculated
- How we optimize Ray worker distribution
- How we handle large datasets efficiently
- Our internal data structures and algorithms
- Performance optimizations and caching strategies

#### **Protobuf Regeneration Strategy:**
```bash
# We can regenerate protobufs without source .proto files
# Users only get compiled Python files, not the original definitions
# This makes reverse engineering much harder
```

### 🔐 Future Security Enhancements

#### **1. Authentication & Authorization**
```python
# Future SDK with auth
import terrafloww as tfw

# API key authentication
tfw.configure(api_key="tfw_live_abc123...")

# Or OAuth flow
tfw.configure(auth_method="oauth", client_id="...")

# Usage-based access control
collection = tfw.load("sentinel-2-l2a")  # Requires "data:read" permission
result = collection.compute()             # Requires "compute:execute" permission
```

#### **2. Server-Side Workflow Validation**
```python
# Current: Client builds workflow
plan = WorkflowPlan(load_step=..., apply_steps=...)

# Future: Server validates and potentially modifies workflows
# - Rate limiting per user/API key
# - Resource quota enforcement
# - Operation whitelisting per subscription tier
```

#### **3. Obfuscated Client SDK**
```python
# Future: Move GeoImageCollection to server-side
class WorkflowBuilder:
    def load(self, collection: str, **kwargs) -> str:
        """Returns workflow_id, not full object"""
        return self._client.create_workflow(...)

    def apply(self, workflow_id: str, operation: str, **kwargs) -> str:
        """Server-side workflow building"""
        return self._client.add_operation(workflow_id, ...)

    def compute(self, workflow_id: str) -> pa.Table:
        """Execute server-side workflow"""
        return self._client.execute_workflow(workflow_id)
```

### 🎯 Reducing Public API Surface

#### **Current Exposure (GeoImageCollection):**
```python
# Users can see our workflow building logic
collection = tfw.load("data")
collection.apply("ndvi", {...})  # Reveals our operation structure
collection.head(10)              # Shows our limiting approach
```

#### **Future Minimal API:**
```python
# Option 1: Workflow IDs only
workflow_id = tfw.create_workflow("sentinel-2-l2a", bbox=[...])
workflow_id = tfw.add_operation(workflow_id, "ndvi", {...})
result = tfw.execute_workflow(workflow_id, limit=10)

# Option 2: Declarative JSON
workflow = {
    "load": {"collection": "sentinel-2-l2a", "bbox": [...]},
    "operations": [{"type": "ndvi", "params": {...}}],
    "limit": 10
}
result = tfw.execute(workflow)

# Option 3: SQL-like DSL
result = tfw.query("""
    LOAD sentinel-2-l2a WHERE bbox=[-74,40.6,-73.9,40.8]
    APPLY ndvi(red_band='B4', nir_band='B8')
    LIMIT 10
""")
```

### 🔒 Implementation Recommendations

#### **Phase 1: Authentication (Immediate)**
1. Add API key support to Processing Engine
2. Validate keys before workflow execution
3. Implement rate limiting and quotas

#### **Phase 2: Server-Side Workflows (6 months)**
1. Move workflow building to server-side
2. Client sends high-level requests, server builds execution plan
3. Reduces exposed logic significantly

#### **Phase 3: Minimal Client (1 year)**
1. Replace GeoImageCollection with simple request builders
2. All logic moves server-side
3. Client becomes pure API wrapper

### 🛡️ Current Security Posture

**✅ Good:**
- No processing algorithms exposed
- No infrastructure details revealed
- Authentication-ready architecture

**⚠️ Medium Risk:**
- API design patterns visible
- Operation names and parameters exposed
- Workflow building logic in client

**🎯 Recommended Actions:**
1. **Immediate**: Add API key authentication
2. **Short-term**: Implement usage quotas and rate limiting
3. **Long-term**: Move workflow building server-side

The current approach provides good security while maintaining developer experience. Future enhancements can further reduce the exposed surface area.
