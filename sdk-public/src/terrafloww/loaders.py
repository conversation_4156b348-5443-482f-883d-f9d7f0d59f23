# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Loaders module for the Terrafloww SDK.

This module provides functions to load geospatial data into GeoImageCollection objects.
These objects represent lazy data streams that can be transformed using the apply methods
and materialized using compute() or iter_batches().
"""

import logging
import os
import asyncio
from typing import Any, Dict, List, Optional, Union

import geopandas as gpd
from shapely.geometry.base import BaseGeometry
from shapely.wkt import loads as wkt_loads

# Internal SDK Components
from .collections import GeoImageCollection

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Default backend service addresses
DEFAULT_PROCESSING_GRPC_TARGET = os.environ.get("TFW_PROCESSING_GRPC_TARGET", "localhost:50051")
DEFAULT_PROCESSING_FLIGHT_TARGET = os.environ.get("TFW_PROCESSING_FLIGHT_TARGET", "grpc+tcp://localhost:50052")


async def _load_async(
    collection: str,
    bands: Optional[List[str]] = None,
    aoi: Optional[Union[BaseGeometry, gpd.GeoDataFrame, gpd.GeoSeries, str]] = None,
    datetime: Optional[str] = None,
    filters: Optional[Dict[str, Any]] = None,
    limit: Optional[int] = None,
    processing_engine_grpc_target: Optional[str] = None,
    processing_engine_flight_target: Optional[str] = None,
) -> 'GeoImageCollection':
    """
    Asynchronous internal function to create a lazy GeoImageCollection.
    
    This function gathers the parameters and constructs the initial load operation.
    Actual data loading and processing is deferred until compute() or iter_batches() is called.
    
    Args:
        collection: Name or ID of the geospatial data collection
        bands: List of band names to select
        aoi: Area of interest as Shapely geometry, WKT string, or GeoPandas object
        datetime: Time range or specific datetime (e.g., "2020-01-01/2020-12-31")
        filters: Additional filters for data selection
        limit: Maximum number of scenes to process
        processing_engine_grpc_target: gRPC endpoint for the processing engine
        processing_engine_flight_target: Arrow Flight endpoint for the processing engine
    
    Returns:
        A lazy GeoImageCollection representing the data loading operation
    """
    logger.info(f"Defining lazy load request for collection: '{collection}'")

    # Resolve backend targets
    final_grpc_target = processing_engine_grpc_target or DEFAULT_PROCESSING_GRPC_TARGET
    final_flight_target = processing_engine_flight_target or DEFAULT_PROCESSING_FLIGHT_TARGET

    # Process AOI if provided
    aoi_wkt_str = None
    aoi_crs_str = None
    if aoi is not None:
        try:
            if isinstance(aoi, str):  # WKT string
                aoi_geom = wkt_loads(aoi)
                aoi_crs_str = "EPSG:4326"  # Assume WKT is in EPSG:4326 if not specified
                logger.info(f"Parsed AOI from WKT string, assuming CRS: {aoi_crs_str}")
            elif isinstance(aoi, BaseGeometry):  # Shapely geometry
                aoi_geom = aoi
                aoi_crs_str = "EPSG:4326"  # Assume Shapely geometry is in EPSG:4326
                logger.info("Using Shapely geometry as AOI, assuming EPSG:4326")
            elif isinstance(aoi, (gpd.GeoDataFrame, gpd.GeoSeries)):  # GeoPandas object
                if not aoi.crs:
                    raise ValueError("GeoPandas object must have a defined CRS")
                aoi_crs_str = aoi.crs.to_string()
                aoi_geom = aoi.unary_union
                logger.info(f"Using GeoPandas geometry as AOI with CRS: {aoi_crs_str}")
            else:
                raise TypeError(f"Unsupported AOI type: {type(aoi)}")
            
            aoi_wkt_str = aoi_geom.wkt
        except Exception as e:
            raise ValueError(f"Failed to process AOI: {e}") from e
    else:
        # Use a default AOI that covers the entire world
        aoi_wkt_str = "POLYGON((-180 -90, 180 -90, 180 90, -180 90, -180 -90))"
        aoi_crs_str = "EPSG:4326"
        logger.info("Using default global AOI since none was provided")

    # Build load operation details
    load_operation_details = {
        "collection": collection,
        "bands": bands or [],
        "aoi_wkt": aoi_wkt_str,
        "aoi_crs": aoi_crs_str,
        "datetime": datetime,
        "filters": filters or {},
        "limit": limit or 0,
    }

    # Create GeoImageCollection instance
    collection_instance = GeoImageCollection(
        operation_type="LOAD",
        operation_details=load_operation_details,
        processing_engine_grpc_target=final_grpc_target,
        processing_engine_flight_target=final_flight_target
    )
    
    logger.info(f"Created lazy GeoImageCollection for '{collection}'")
    return collection_instance


def load(
    collection: str,
    bands: Optional[List[str]] = None,
    aoi: Optional[Union[BaseGeometry, gpd.GeoDataFrame, gpd.GeoSeries, str]] = None,
    datetime: Optional[str] = None,
    filters: Optional[Dict[str, Any]] = None,
    limit: Optional[int] = None,
    processing_engine_grpc_target: Optional[str] = None,
    processing_engine_flight_target: Optional[str] = None,
) -> 'GeoImageCollection':
    """
    Load geospatial data as a lazy GeoImageCollection.
    
    This is the main entry point for starting a data processing workflow with the SDK.
    The returned collection represents a lazy computation graph that will be executed
    when compute() or iter_batches() is called.
    
    Args:
        collection: Name or ID of the geospatial data collection
        bands: List of band names to select
        aoi: Area of interest as Shapely geometry, WKT string, or GeoPandas object
        datetime: Time range or specific datetime (e.g., "2020-01-01/2020-12-31")
        filters: Additional filters for data selection
        limit: Maximum number of scenes to process
        processing_engine_grpc_target: gRPC endpoint for the processing engine
        processing_engine_flight_target: Arrow Flight endpoint for the processing engine
    
    Returns:
        A lazy GeoImageCollection representing the data loading operation
    
    Examples:
        >>> import terrafloww as tfw
        >>> collection = tfw.load("sentinel-2-l2a", bands=["B4", "B8"])
        >>> # Apply NDVI operation
        >>> ndvi_collection = collection.apply("ndvi", {"red_band": "B4", "nir_band": "B8"})
        >>> # Execute computation
        >>> result = ndvi_collection.compute()
    """
    return asyncio.run(
        _load_async(
            collection=collection,
            bands=bands,
            aoi=aoi,
            datetime=datetime,
            filters=filters,
            limit=limit,
            processing_engine_grpc_target=processing_engine_grpc_target,
            processing_engine_flight_target=processing_engine_flight_target,
        )
    )
