# Terrafloww SDK

Client library for Terrafloww Platform, providing access to geospatial data processing and analysis capabilities.

## Installation

```bash
pip install terrafloww-sdk
```

## Usage

```python
from terrafloww.sdk import Collection, Loader

# Load a collection
collection = Collection.from_id("landsat-8")

# Create a loader
loader = Loader(collection)

# Load data for a specific area
data = loader.load(bounds=(x_min, y_min, x_max, y_max))

# Perform operations
result = data.apply_operation("ndvi")
```

## Examples

See the `examples` directory for more detailed usage examples.
