# sdk/pyproject.toml
[build-system]
requires = ["setuptools>=61.0"] # Minimum version for editable installs with paths
build-backend = "setuptools.build_meta"

[project]
name = "terrafloww-sdk"
version = "0.2.0"
description = "Python SDK for the Terrafloww Geospatial AI Platform."
readme = "README.md" # Optional
license = {text = "Apache-2.0"} # Specify your license
requires-python = ">=3.10"
dependencies = [
    # External dependencies with consistent versions
    "pyarrow>=15.0",
    "numpy>=1.20",
    "geopandas>=0.12",
    "shapely>=2.0",
    "httpx>=0.26.0",  # Updated to match version in other components
    "grpcio>=1.50",
    "protobuf>=4.0",
    "pyarrow[flight]>=15.0",
    "google-api-python-client",
    "googleapis-common-protos",
    
    # Local packages
    # These will be installed in development mode via install_dev.sh
    # In production builds, these would be proper package dependencies
    "tfw-raster-schemas>=0.1.0",
    "tfw-core-utils>=0.1.0",
    "tfw-engine-core>=0.1.0",
    "tfw-processing-api>=0.1.0",
    "tfw-ray-utils>=0.1.0",
]

[project.optional-dependencies]
dev = [
    "pytest",
    "pytest-asyncio",
    # Add other dev tools like ruff, mypy
]
torch = [
    "torch>=2.0"
]

# Add entry points or scripts if needed
# [project.scripts]
# tfw = "terrafloww.sdk.cli:main"

[tool.setuptools]
# Add package data if necessary
# package-data = {"terrafloww.sdk": ["py.typed"]}

[tool.setuptools.packages.find]
where = ["src"]