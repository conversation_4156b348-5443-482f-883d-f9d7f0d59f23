"""
Unit test: test the GeoImageCollection operations with server-side functions.
"""

import pytest
import pyarrow as pa
from terrafloww.sdk import load
from terrafloww.sdk.tesseract.operations import calculate_ndvi_op
from shapely.geometry import box

# path or collection name you know is in your dev bucket / mini corpus
_COLLECTION = "sentinel-2-l2a"   # adjust if needed
_SAMPLE_AOI = box(-74.05, 40.75, -73.95, 40.85).wkt

def test_geoimagecollection_apply_ndvi():
    """Test that applying NDVI operation works with the server-side approach."""
    # Get function_id and parameters from the operations helper
    function_id, params = calculate_ndvi_op(red_band_name="red", nir_band_name="nir")
    
    # Build the collection and apply the operation
    collection = (
        load(_COLLECTION)
        .filter(
            aoi=_SAMPLE_AOI,
            date_range="2024-03-01/2024-03-03",
        )
        .apply(function_id=function_id, parameters=params)
        .head(2)
    )
    
    # Verify the workflow plan has the correct function_id and parameters
    plan = collection._build_workflow_plan()
    assert len(plan.apply_steps) == 1
    assert plan.apply_steps[0].function_id == "terrafloww.spectral.ndvi"
    
    # Note: We don't call compute() here as it may not be fully implemented yet
    # This test focuses on verifying the correct construction of the workflow plan
