
def test_ndvi():
    import re
    import os

    from terrafloww.sdk import load
    from terrafloww.sdk.tesseract.operations.spectral import calculate_ndvi_op

    # Get endpoints from environment variables or use cloud defaults
    grpc_target = os.getenv("PROCESSING_ENGINE_GRPC_TARGET", "139.59.55.173:50051")
    flight_target = os.getenv("PROCESSING_ENGINE_FLIGHT_TARGET", "grpc+tcp://139.59.55.173:50052")

    print(f"Using gRPC target: {grpc_target}")
    print(f"Using Flight target: {flight_target}")

    # Load data with required bands - using the common names that exist in the data
    # Use specific California region and June 2024 timeframe to match ingested data
    from shapely.geometry import box

    # Create AOI for California region (San Francisco Bay Area)
    california_aoi = box(-122.5, 37.7, -122.3, 37.9)

    collection = load(
        "sentinel-2-l2a",
        bands=["red", "nir"],  # Using common names that match the actual data
        aoi=california_aoi,  # California region (San Francisco Bay Area)
        datetime="2024-06-01/2024-06-23",  # June 2024 timeframe
        processing_engine_grpc_target=grpc_target,
        processing_engine_flight_target=flight_target
    )

    # Apply NDVI operation using the same common names as loaded
    # The NDVI kernel will handle the common name to band ID mapping
    function_id, params = calculate_ndvi_op(red_band_name="red", nir_band_name="nir")
    collection = collection.apply(function_id=function_id, parameters=params)

    # Apply head limit to get exactly 2 unique spatial windows
    collection = collection.head(2)

    # Execute computation
    result = collection.compute()

    # Verify results
    print(f"Result table shape: {result.num_rows} rows, {len(result.column_names)} columns")
    print(f"Columns: {result.column_names}")
    
    # Since we requested head(2), we should get exactly 2 unique spatial windows
    # Each window will have all required bands (red, nir) processed together
    assert result.num_rows == 2, "Expected exactly 2 chunks (2 windows with multi-band data + ndvi)"
    print("result num rows is as expected")
    assert "ndvi" in result.column_names, "NDVI column should be present"
    print("ndvi present in result")

    # Verify unique spatial windows
    unique_windows = set()
    for row in result.to_pandas().itertuples():
        # chunk_id is now: {scene_id}_T{tile_r:02d}{tile_c:02d}_{suffix}
        m = re.match(r'(.+)_T(\d{2})(\d{2})_', row.chunk_id)
        assert m, f"Unexpected chunk_id format: {row.chunk_id}"
        scene_id = m.group(1)
        tile_r, tile_c = int(m.group(2)), int(m.group(3))
        unique_windows.add((scene_id, tile_r, tile_c))

    assert len(unique_windows) == 2, (
        f"Expected exactly 2 unique windows, got {len(unique_windows)}"
    )
    print("unique windows are as expected")

test_ndvi()