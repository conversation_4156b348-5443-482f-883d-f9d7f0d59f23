# sdk/src/terrafloww/sdk/tesseract/loaders.py
"""
Loaders module for the Terrafloww SDK.

This module provides functions to load geospatial data into GeoImageCollection objects.
These objects represent lazy data streams that can be transformed using the apply methods
and materialized using compute() or iter_batches().
"""

import logging
import os, sys
import asyncio
from typing import Any, Dict, List, Optional, Union

import httpx
import geopandas as gpd
from shapely.geometry.base import BaseGeometry
from shapely.wkt import loads as wkt_loads

# Internal SDK Components
from .collections import GeoImageCollection
# Schema imports
from tfw_raster_schemas.raster import RASTER_CHUNK_SCHEMA

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s", stream=sys.stdout
)
logger = logging.getLogger(__name__)

# Configuration defaults
DEFAULT_CATALOG_IDENTIFIER = os.environ.get(
    "TFW_DEFAULT_CATALOG", 
    "/tmp/platform_delta_tables/ext_stac_datasets" # Default for local development
)

# Default backend service addresses
DEFAULT_PROCESSING_GRPC_TARGET = os.environ.get("TFW_PROCESSING_GRPC_TARGET", "localhost:50051")
DEFAULT_PROCESSING_FLIGHT_TARGET = os.environ.get("TFW_PROCESSING_FLIGHT_TARGET", "grpc+tcp://localhost:50052")


async def _load_async(
    collection: str,
    bands: Optional[List[str]] = None,
    aoi: Optional[Union[BaseGeometry, gpd.GeoDataFrame, gpd.GeoSeries, str]] = None,
    datetime: Optional[str] = None,
    filters: Optional[Dict[str, Any]] = None,
    limit: Optional[int] = None,
    http_client_for_api_calls: Optional[httpx.AsyncClient] = None,
    metadata_client_for_api_calls: Optional[Any] = None,
    catalog_identifier: Optional[str] = None,
    processing_engine_grpc_target: Optional[str] = None,
    processing_engine_flight_target: Optional[str] = None,
) -> 'GeoImageCollection':
    """
    Asynchronous internal function to create a lazy GeoImageCollection.
    
    This function gathers the parameters and constructs the initial load operation.
    Actual data loading and processing is deferred until compute() or iter_batches() is called.
    
    Args:
        collection: Name or ID of the geospatial data collection
        bands: List of band names to select
        aoi: Area of interest as Shapely geometry, WKT string, or GeoPandas object
        datetime: Time range or specific datetime (e.g., "2020-01-01/2020-12-31")
        filters: Additional filters for data selection
        limit: Maximum number of scenes to process
        http_client_for_api_calls: Optional HTTP client for API calls
        metadata_client_for_api_calls: Optional metadata service client
        catalog_identifier: Source catalog ID
        processing_engine_grpc_target: gRPC endpoint for the processing engine
        processing_engine_flight_target: Arrow Flight endpoint for the processing engine
    
    Returns:
        A lazy GeoImageCollection representing the data loading operation
    """
    logger.info(f"Defining lazy load request for collection: '{collection}'")

    # Manage HTTP client lifecycle
    _http_client = http_client_for_api_calls
    should_close_http_client = False
    if not _http_client:
        logger.debug("Creating temporary httpx client for API interactions")
        _http_client = httpx.AsyncClient(timeout=30.0)
        should_close_http_client = True
    
    # Resolve backend targets
    final_grpc_target = processing_engine_grpc_target or DEFAULT_PROCESSING_GRPC_TARGET
    final_flight_target = processing_engine_flight_target or DEFAULT_PROCESSING_FLIGHT_TARGET
    final_catalog_identifier = catalog_identifier or DEFAULT_CATALOG_IDENTIFIER

    # Process AOI if provided
    aoi_wkt_str = None
    aoi_crs_str = None
    if aoi is not None:
        try:
            if isinstance(aoi, str):  # WKT string
                aoi_geom = wkt_loads(aoi)
                aoi_crs_str = "EPSG:4326"  # Assume WKT is in EPSG:4326 if not specified
                logger.info(f"Parsed AOI from WKT string, assuming CRS: {aoi_crs_str}")
            elif isinstance(aoi, BaseGeometry):  # Shapely geometry
                aoi_geom = aoi
                aoi_crs_str = "EPSG:4326"  # Assume Shapely geometry is in EPSG:4326
                logger.info("Using Shapely geometry as AOI, assuming EPSG:4326")
            elif isinstance(aoi, (gpd.GeoDataFrame, gpd.GeoSeries)):  # GeoPandas object
                if not aoi.crs:
                    raise ValueError("GeoPandas object must have a defined CRS")
                aoi_crs_str = aoi.crs.to_string()
                aoi_geom = aoi.unary_union
                logger.info(f"Using GeoPandas geometry as AOI with CRS: {aoi_crs_str}")
            else:
                raise TypeError(f"Unsupported AOI type: {type(aoi)}")
            
            aoi_wkt_str = aoi_geom.wkt
        except Exception as e:
            if should_close_http_client and _http_client:
                await _http_client.aclose()
            raise ValueError(f"Failed to process AOI: {e}") from e
    else:
        # Use a default AOI that covers the entire world
        aoi_wkt_str = "POLYGON((-180 -90, 180 -90, 180 90, -180 90, -180 -90))"
        aoi_crs_str = "EPSG:4326"
        logger.info("Using default global AOI since none was provided")

    # Build load operation details
    load_operation_details = {
        "collection": collection,
        "bands": bands or [],
        "aoi_wkt": aoi_wkt_str,
        "aoi_crs": aoi_crs_str,
        "datetime": datetime,
        "filters": filters or {},
        "limit": limit or 0,
        "catalog_identifier": final_catalog_identifier,
    }

    # Create GeoImageCollection instance
    collection_instance = GeoImageCollection(
        operation_type="LOAD",
        operation_details=load_operation_details,
        expected_output_schema=RASTER_CHUNK_SCHEMA,
        parent_node=None,
        http_client=_http_client,
        metadata_client=metadata_client_for_api_calls,
        should_close_http_client=should_close_http_client,
        window_limit=None,
        processing_engine_grpc_target=final_grpc_target,
        processing_engine_flight_target=final_flight_target
    )
    
    logger.info(f"Created lazy GeoImageCollection for '{collection}'")
    return collection_instance


def load(
    collection: str,
    bands: Optional[List[str]] = None,
    aoi: Optional[Union[BaseGeometry, gpd.GeoDataFrame, gpd.GeoSeries, str]] = None,
    datetime: Optional[str] = None,
    filters: Optional[Dict[str, Any]] = None,
    limit: Optional[int] = None,
    http_client: Optional[httpx.AsyncClient] = None,
    metadata_client: Optional[Any] = None,
    catalog_identifier: Optional[str] = None,
    processing_engine_grpc_target: Optional[str] = None,
    processing_engine_flight_target: Optional[str] = None,
) -> 'GeoImageCollection':
    """
    Load geospatial data as a lazy GeoImageCollection.
    
    This is the main entry point for starting a data processing workflow with the SDK.
    The returned collection represents a lazy computation graph that will be executed
    when compute() or iter_batches() is called.
    
    Args:
        collection: Name or ID of the geospatial data collection
        bands: List of band names to select
        aoi: Area of interest as Shapely geometry, WKT string, or GeoPandas object
        datetime: Time range or specific datetime (e.g., "2020-01-01/2020-12-31")
        filters: Additional filters for data selection
        limit: Maximum number of scenes to process
        http_client: Optional HTTP client for API calls
        metadata_client: Optional metadata service client
        catalog_identifier: Source catalog ID
        processing_engine_grpc_target: gRPC endpoint for the processing engine
        processing_engine_flight_target: Arrow Flight endpoint for the processing engine
    
    Returns:
        A lazy GeoImageCollection representing the data loading operation
    
    Examples:
        >>> from terrafloww.sdk import load
        >>> collection = load("sentinel-2-l2a", bands=["B4", "B8"])
        >>> # Apply NDVI operation
        >>> from terrafloww.sdk.tesseract.operations.spectral import calculate_ndvi_op
        >>> ndvi_func_id, ndvi_params = calculate_ndvi_op(red_band_name="B4", nir_band_name="B8")
        >>> ndvi_collection = collection.apply(function_id=ndvi_func_id, parameters=ndvi_params)
        >>> # Execute computation
        >>> result = ndvi_collection.compute()
    """
    # Handle asyncio event loop for the synchronous wrapper
    try:
        loop = asyncio.get_running_loop()
        if loop.is_running():
            # This case is tricky - we're inside a running event loop (like in a Jupyter notebook)
            # For simplicity in this version, we'll just warn about potential issues
            logger.warning("load() called from within a running event loop. This might cause issues.")
    except RuntimeError:
        # No running event loop, which is the expected case for synchronous API usage
        pass
    
    return asyncio.run(
        _load_async(
            collection=collection,
            bands=bands,
            aoi=aoi,
            datetime=datetime,
            filters=filters,
            limit=limit,
            http_client_for_api_calls=http_client,
            metadata_client_for_api_calls=metadata_client,
            catalog_identifier=catalog_identifier,
            processing_engine_grpc_target=processing_engine_grpc_target,
            processing_engine_flight_target=processing_engine_flight_target,
        )
    )
