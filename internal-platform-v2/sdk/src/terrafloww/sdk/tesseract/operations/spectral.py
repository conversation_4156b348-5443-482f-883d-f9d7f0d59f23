"""
Spectral operations for the Terrafloww SDK.

This module provides helper functions for spectral operations that can be applied
to GeoImageCollection objects. These helpers follow the API-first design approach,
where the actual implementation of the operations is on the server side.

The implementation of these operations is in the engine_core module, specifically
in the process.py file where kernels are registered with the @register decorator.

The canonical schemas for these operations are defined in tfw_raster_schemas.operations.
"""

from typing import Dict, Tuple, Optional, Union, List
from tfw_raster_schemas.operations import OPERATIONS_SCHEMA

# Function ID constants for server-side operations
# These must match the function IDs registered in the engine_core
# We use the namespaced versions to provide better context
NDVI_FUNCTION_ID = "terrafloww.spectral.ndvi"  # Maps to @register("ndvi") in engine_core/process.py
EVI_FUNCTION_ID = "terrafloww.spectral.evi"  # Maps to @register("evi") in engine_core/process.py
NDWI_FUNCTION_ID = "terrafloww.spectral.ndwi"  # Maps to @register("ndwi") in engine_core/process.py
NORMALIZED_DIFF_FUNCTION_ID = "terrafloww.spectral.normalized_diff"  # Maps to @register("normalized_diff")

def calculate_ndvi_op(red_band_name: str = "red", nir_band_name: str = "nir") -> Tuple[str, Dict]:
    """
    Returns parameters for an NDVI operation step.
    
    NDVI (Normalized Difference Vegetation Index) is calculated as:
    NDVI = (NIR - Red) / (NIR + Red)
    
    This is a specialized case of the normalized difference operation.
    
    Args:
        red_band_name: Name of the red band in the input data
        nir_band_name: Name of the near-infrared band in the input data
        
    Returns:
        A tuple of (function_id, parameters) to be passed to GeoImageCollection.apply()
        
    Example:
        >>> from terrafloww.sdk import load
        >>> from terrafloww.sdk.tesseract.operations import calculate_ndvi_op
        >>> function_id, params = calculate_ndvi_op(red_band_name="B4", nir_band_name="B8")
        >>> result = load("sentinel-2-l2a").apply(function_id=function_id, parameters=params)
    """
    return NDVI_FUNCTION_ID, {
        "red_band": red_band_name,
        "nir_band": nir_band_name
    }


def calculate_evi_op(
    red_band_name: str = "red", 
    nir_band_name: str = "nir", 
    blue_band_name: str = "blue",
    g: float = 2.5,
    c1: float = 6.0,
    c2: float = 7.5,
    l: float = 1.0
) -> Tuple[str, Dict]:
    """
    Returns parameters for an EVI (Enhanced Vegetation Index) operation step.
    
    EVI = g * (NIR - Red) / (NIR + c1 * Red - c2 * Blue + l)
    
    Args:
        red_band_name: Name of the red band in the input data
        nir_band_name: Name of the near-infrared band in the input data
        blue_band_name: Name of the blue band in the input data
        g: Gain factor
        c1: Coefficient for the red band
        c2: Coefficient for the blue band
        l: Canopy background adjustment
        
    Returns:
        A tuple of (function_id, parameters) to be passed to GeoImageCollection.apply()
        
    Example:
        >>> from terrafloww.sdk import load
        >>> from terrafloww.sdk.tesseract.operations import calculate_evi_op
        >>> function_id, params = calculate_evi_op(red_band_name="B4", nir_band_name="B8", blue_band_name="B2")
        >>> result = load("sentinel-2-l2a").apply(function_id=function_id, parameters=params)
    """
    return EVI_FUNCTION_ID, {
        "red_band": red_band_name,
        "nir_band": nir_band_name,
        "blue_band": blue_band_name,
        "g": g,
        "c1": c1,
        "c2": c2,
        "l": l
    }


def calculate_ndwi_op(green_band_name: str = "green", nir_band_name: str = "nir") -> Tuple[str, Dict]:
    """
    Returns parameters for an NDWI operation step.
    
    NDWI (Normalized Difference Water Index) is calculated as:
    NDWI = (Green - NIR) / (Green + NIR)
    
    This is a specialized case of the normalized difference operation.
    
    Args:
        green_band_name: Name of the green band in the input data
        nir_band_name: Name of the near-infrared band in the input data
        
    Returns:
        A tuple of (function_id, parameters) to be passed to GeoImageCollection.apply()
        
    Example:
        >>> from terrafloww.sdk import load
        >>> from terrafloww.sdk.tesseract.operations import calculate_ndwi_op
        >>> function_id, params = calculate_ndwi_op(green_band_name="B3", nir_band_name="B8")
        >>> result = load("sentinel-2-l2a").apply(function_id=function_id, parameters=params)
    """
    return NDWI_FUNCTION_ID, {
        "green_band": green_band_name,
        "nir_band": nir_band_name
    }


def normalized_difference_op(
    band_a: str,
    band_b: str,
    output_name: Optional[str] = None,
    mask_zeros: bool = True
) -> Tuple[str, Dict]:
    """
    Returns parameters for a generic normalized difference operation.
    
    Normalized Difference = (A - B) / (A + B)
    
    This is a more generic version that can be used to create custom indices.
    
    Args:
        band_a: Name of the first band
        band_b: Name of the second band
        output_name: Optional name for the output band (defaults to "norm_diff")
        mask_zeros: Whether to mask out zeros in the denominator (A + B)
        
    Returns:
        A tuple of (function_id, parameters) to be passed to GeoImageCollection.apply()
        
    Example:
        >>> from terrafloww.sdk import load
        >>> from terrafloww.sdk.tesseract.operations import normalized_difference_op
        >>> # Create a custom index
        >>> function_id, params = normalized_difference_op(band_a="B8", band_b="B11", output_name="custom_index")
        >>> result = load("sentinel-2-l2a").apply(function_id=function_id, parameters=params)
    """
    return NORMALIZED_DIFF_FUNCTION_ID, {
        "band_a": band_a,
        "band_b": band_b,
        "output_name": output_name or "norm_diff",
        "mask_zeros": mask_zeros
    }
