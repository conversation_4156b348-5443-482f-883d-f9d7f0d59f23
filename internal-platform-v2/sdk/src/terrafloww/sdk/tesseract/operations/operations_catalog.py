"""
Operations catalog for the Terrafloww SDK.

This module provides a catalog of operations that can be applied to GeoImageCollection objects.
It references the canonical operation schemas defined in tfw_raster_schemas.operations.

The actual implementation of these operations is in the engine_core module, specifically
in the process.py file where kernels are registered with the @register decorator.
"""

from typing import Dict, Any, List, Optional
from tfw_raster_schemas.operations import (
    OPERATIONS_SCHEMA,
    get_operation_schema,
    list_available_operations,
    get_operations_by_category,
    get_required_bands_for_operation,
)

# Re-export the canonical schema functions
__all__ = [
    "get_operation_info",
    "list_available_operations",
    "get_operations_by_category",
    "get_required_bands_for_operation",
    "OPERATIONS_CATALOG",
]

# Alias for backward compatibility
OPERATIONS_CATALOG = OPERATIONS_SCHEMA

def get_operation_info(operation_id: str) -> Optional[Dict[str, Any]]:
    """
    Get information about a registered operation.
    
    This is an alias for get_operation_schema for backward compatibility.
    
    Args:
        operation_id: The ID of the operation to get information about
        
    Returns:
        A dictionary with information about the operation, or None if not found
    """
    return get_operation_schema(operation_id)

# Additional SDK-specific catalog functions could be added here
def get_example_usage(operation_id: str) -> Optional[str]:
    """
    Get example usage code for an operation.
    
    Args:
        operation_id: The ID of the operation
        
    Returns:
        A string with example code, or None if not available
    """
    examples = {
        "ndvi": """
from terrafloww.sdk import load
from terrafloww.sdk.tesseract.operations import calculate_ndvi_op

# Get the function ID and parameters
function_id, params = calculate_ndvi_op(red_band_name="B4", nir_band_name="B8")

# Apply the operation
result = load("sentinel-2-l2a").apply(function_id=function_id, parameters=params)
""",
        "evi": """
from terrafloww.sdk import load
from terrafloww.sdk.tesseract.operations import calculate_evi_op

# Get the function ID and parameters
function_id, params = calculate_evi_op(
    red_band_name="B4", 
    nir_band_name="B8", 
    blue_band_name="B2"
)

# Apply the operation
result = load("sentinel-2-l2a").apply(function_id=function_id, parameters=params)
""",
        "ndwi": """
from terrafloww.sdk import load
from terrafloww.sdk.tesseract.operations import calculate_ndwi_op

# Get the function ID and parameters
function_id, params = calculate_ndwi_op(green_band_name="B3", nir_band_name="B8")

# Apply the operation
result = load("sentinel-2-l2a").apply(function_id=function_id, parameters=params)
""",
        "normalized_diff": """
from terrafloww.sdk import load
from terrafloww.sdk.tesseract.operations import normalized_difference_op

# Create a custom index
function_id, params = normalized_difference_op(
    band_a="B8", 
    band_b="B11", 
    output_name="custom_index"
)

# Apply the operation
result = load("sentinel-2-l2a").apply(function_id=function_id, parameters=params)
"""
    }
    
    return examples.get(operation_id)
