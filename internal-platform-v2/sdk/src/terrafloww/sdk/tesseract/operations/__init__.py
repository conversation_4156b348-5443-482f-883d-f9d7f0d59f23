"""
Operations module for the Terrafloww SDK.

This module provides helper functions for common operations that can be applied
to GeoImageCollection objects. These helpers follow the API-first design approach,
where the actual implementation of the operations is on the server side.

The helpers in this module return function_id strings and parameter dictionaries
that can be passed to GeoImageCollection.apply().

The canonical schemas for operations are defined in tfw_raster_schemas.operations.
"""

from .spectral import (
    NDVI_FUNCTION_ID,
    EVI_FUNCTION_ID,
    NDWI_FUNCTION_ID,
    NORMALIZED_DIFF_FUNCTION_ID,
    calculate_ndvi_op,
    calculate_evi_op,
    calculate_ndwi_op,
    normalized_difference_op,
)

from .operations_catalog import (
    get_operation_info,
    list_available_operations,
    get_operations_by_category,
    get_required_bands_for_operation,
    get_example_usage,
    OPERATIONS_CATALOG,
)

__all__ = [
    # Function ID constants
    "NDVI_FUNCTION_ID",
    "EVI_FUNCTION_ID",
    "NDWI_FUNCTION_ID",
    "NORMALIZED_DIFF_FUNCTION_ID",
    
    # Operation helper functions
    "calculate_ndvi_op",
    "calculate_evi_op",
    "calculate_ndwi_op",
    "normalized_difference_op",
    
    # Operations catalog functions
    "get_operation_info",
    "list_available_operations",
    "get_operations_by_category",
    "get_required_bands_for_operation",
    "get_example_usage",
    "OPERATIONS_CATALOG",
]
