# src/terrafloww/sdk/tesseract/collections.py
import logging
import pyarrow as pa
import asyncio
import numpy as np
import inspect
import os
from typing import Any, Callable, List, Optional, Dict, DefaultDict, Tuple, AsyncIterator, Union, AsyncGenerator
from uuid import uuid4
import hashlib
from collections import defaultdict
from rasterio.windows import Window as RasterioWindow # Import with alias

# --- Internal SDK Components ---
# from .iterators import _generate_definition_tables
# from .schemas import WINDOW_DEFINITION_SCHEMA
# from tfw_raster_schemas.raster import RASTER_CHUNK_SCHEMA
# from shapely.wkt import loads as wkt_loads
# from rasterio.transform import Affine
import httpx, sys

# from terrafloww.engine_core.fetch import fetch_raw_window_data
# from terrafloww.engine_core.process import _decode_apply_predictor
# from terrafloww.engine_core.grid import calculate_window_bounds
# from terrafloww.engine_core.utils import _np_chunk_to_arrow_list_components

from google.protobuf.struct_pb2 import Struct # Import Struct


import grpc.aio as grpc_async
from pyarrow import flight as fl

# --- Internal SDK Components ---
# from raster import RASTER_CHUNK_SCHEMA
# Import generated protobuf/gRPC code (using the library package now)
from terrafloww.processing_engine.v1 import processing_engine_pb2
from terrafloww.processing_engine.v1 import processing_engine_pb2_grpc
# Keep converters
# from .ml.converters import arrow_to_numpy, numpy_to_arrow

RAY_INSTALLED = False
# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s", stream=sys.stdout
)
logger = logging.getLogger(__name__)

# Default processing engine targets from environment variables
DEFAULT_GRPC_TARGET = os.environ.get("TFW_PROCESSING_GRPC_TARGET", "localhost:50051")
DEFAULT_FLIGHT_TARGET = os.environ.get("TFW_PROCESSING_FLIGHT_TARGET", "grpc+tcp://localhost:50052") # Correct scheme for Flight

class GeoImageCollection:
    def __init__(
        self,
        operation_type: str, 
        operation_details: Dict[str, Any], 
        expected_output_schema: pa.Schema,
        parent_node: Optional['GeoImageCollection'] = None,
        http_client: Optional[httpx.AsyncClient] = None, 
        metadata_client: Optional[Any] = None,
        should_close_http_client: bool = False, 
        window_limit: Optional[int] = None, 
        node_id: Optional[str] = None,
        processing_engine_grpc_target: Optional[str] = None,
        processing_engine_flight_target: Optional[str] = None
    ):
        self._operation_type = operation_type
        self._operation_details = operation_details
        self._expected_output_schema = expected_output_schema
        self._parent_node = parent_node
        self._http_client = http_client
        self._metadata_client = metadata_client
        self._should_close_http_client = should_close_http_client
        self._window_limit = window_limit
        self._node_id = node_id or f"{operation_type}_{uuid4().hex[:8]}"
        self._result_cache: Optional[Union[pa.Table, asyncio.Future]] = None
        self._iterator_running = False
        
        # Set processing engine targets, inheriting from parent or using defaults
        self._grpc_target = (
            processing_engine_grpc_target or 
            (parent_node._grpc_target if parent_node else None) or 
            DEFAULT_GRPC_TARGET
        )
        self._flight_target = (
            processing_engine_flight_target or 
            (parent_node._flight_target if parent_node else None) or 
            DEFAULT_FLIGHT_TARGET
        )
        
        # Build pipeline steps from load, storing necessary details for workflow planning
        self._pipeline_steps_from_load: List[Dict[str, Any]] = []
        curr = self
        while curr is not None:
            step_details = {
                "operation_type": curr._operation_type,
                "operation_details": curr._operation_details,
                "node_id": curr._node_id,
                "output_schema": curr._expected_output_schema
            }
            self._pipeline_steps_from_load.insert(0, step_details)
            curr = curr._parent_node
            
        logger.debug(f"Initialized GeoImageCollection Node: {self._node_id} (Type: {self._operation_type})")

    def apply(self, function_id: str = None, parameters: Dict[str, Any] = None, user_func: Callable = None, 
             *args, distributed: Optional[bool] = None, output_schema: Optional[pa.Schema] = None, 
             **kwargs) -> 'GeoImageCollection':
        """
        Apply an operation to the GeoImageCollection.
        
        This method supports two approaches:
        1. API-first approach: Pass a function_id and parameters to be executed on the server
        2. Client-side approach: Pass a user_func to be executed locally
        
        For clearer API design, consider using the specialized methods:
        - apply_operation(function_id, parameters) for server-side operations
        - apply_custom(user_func, *args, **kwargs) for client-side functions
        
        Args:
            function_id: ID of the server-side function to apply (e.g., "terrafloww.spectral.ndvi")
            parameters: Dictionary of parameters for the server-side function
            user_func: Local Python function to apply (alternative to function_id)
            *args: Positional arguments for user_func (ignored if using function_id)
            distributed: Hint for distributed execution
            output_schema: Expected output schema
            **kwargs: Keyword arguments for user_func (ignored if using function_id)
            
        Returns:
            A new GeoImageCollection with the operation applied
            
        Examples:
            # Server-side approach (API-first)
            from terrafloww.sdk.tesseract.operations.spectral import calculate_ndvi_op
            function_id, params = calculate_ndvi_op(red_band_name="B4", nir_band_name="B8")
            result = collection.apply(function_id=function_id, parameters=params)
            
            # Alternative direct approach
            result = collection.apply(function_id="ndvi", 
                                    parameters={"red_band": "B4", "nir_band": "B8"})
            
            # Client-side approach (legacy)
            def my_function(data):
                return data * 2
            result = collection.apply(user_func=my_function)
        """
        if function_id is not None:
            # API-first approach: Use server-side function
            return self.apply_operation(function_id, parameters, distributed=distributed, output_schema=output_schema)
        elif user_func is not None:
            # Client-side approach: Use local function
            return self.apply_custom(user_func, *args, distributed=distributed, output_schema=output_schema, **kwargs)
        else:
            raise ValueError("Either function_id or user_func must be provided")
    
    def apply_operation(self, function_id: str, parameters: Dict[str, Any] = None, 
                      distributed: Optional[bool] = None, output_schema: Optional[pa.Schema] = None) -> 'GeoImageCollection':
        """
        Apply a registered server-side operation to the GeoImageCollection.
        
        This method follows the API-first approach, where operations are registered on the server
        and identified by their function_id. The actual implementation of the operations is in
        the engine_core module.
        
        Args:
            function_id: ID of the server-side function to apply (e.g., "ndvi")
            parameters: Dictionary of parameters for the server-side function
            distributed: Hint for distributed execution
            output_schema: Expected output schema
            
        Returns:
            A new GeoImageCollection with the operation applied
            
        Examples:
            # Using a helper function to get function_id and parameters
            from terrafloww.sdk.tesseract.operations import calculate_ndvi_op
            function_id, params = calculate_ndvi_op(red_band_name="B4", nir_band_name="B8")
            result = collection.apply_operation(function_id, params)
            
            # Direct approach
            result = collection.apply_operation("ndvi", {"red_band": "B4", "nir_band": "B8"})
        """
        # Log the operation being added
        logger.info(f"Adding APPLY_FUNC step with function_id: {function_id}")
        
        # Try to validate parameters against schema if available
        try:
            from tfw_raster_schemas.operations import get_operation_schema
            schema = get_operation_schema(function_id)
            if schema and parameters:
                # Basic validation could be added here
                pass
        except (ImportError, Exception) as e:
            logger.debug(f"Could not validate operation parameters: {e}")
        
        # Use the same schema as input by default
        final_output_schema = output_schema or self._expected_output_schema
        
        # Create operation details with consistent naming
        operation_details = {
            "function_id": function_id,  # Store with consistent name
            "kwargs": parameters or {},  # Parameters for the server-side function
            "distributed_hint": distributed
        }
        
        return GeoImageCollection(
            operation_type="APPLY_FUNC", 
            operation_details=operation_details, 
            expected_output_schema=final_output_schema, 
            parent_node=self, 
            http_client=self._http_client, 
            metadata_client=self._metadata_client, 
            should_close_http_client=False, 
            window_limit=self._window_limit,
            processing_engine_grpc_target=self._grpc_target,
            processing_engine_flight_target=self._flight_target
        )
    
    def apply_custom(self, user_func: Callable, *args, distributed: Optional[bool] = None, 
                    output_schema: Optional[pa.Schema] = None, **kwargs) -> 'GeoImageCollection':
        """
        Apply a custom user-defined function to the GeoImageCollection.
        
        This method follows the client-side approach, where the function is defined by the user
        and executed locally or remotely depending on the distributed hint.
        
        Args:
            user_func: The Python function to apply to the data
            *args: Positional arguments to pass to the function
            distributed: Hint for distributed execution
            output_schema: Expected output schema
            **kwargs: Keyword arguments to pass to the function
            
        Returns:
            A new GeoImageCollection with the function applied
            
        Examples:
            def my_function(data):
                return data * 2
                
            result = collection.apply_custom(my_function)
        """
        # This delegates to the existing implementation
        return self._apply_async(user_func, *args, distributed=distributed, 
                               output_schema=output_schema, **kwargs)

    def _apply_async(
        self, 
        user_func: Callable, 
        *args, 
        distributed: Optional[bool] = None, 
        output_schema: Optional[pa.Schema] = None, 
        **kwargs
    ) -> 'GeoImageCollection':
        logger.info(f"Adding APPLY_FUNC step: {getattr(user_func, '__name__', 'unknown_function')}")
        inferred_schema = self._infer_output_schema(user_func)
        final_output_schema = output_schema or inferred_schema or self._expected_output_schema
        func_name = getattr(user_func, '__name__', 'unknown_function')
        
        if final_output_schema is None:
            raise ValueError(f"Output schema could not be determined for apply step: {func_name}")
        elif final_output_schema == self._expected_output_schema and not output_schema and not inferred_schema:
            logger.warning(f"Could not infer output schema for {func_name}. Using input schema {self._expected_output_schema.names}. Provide type hints or explicit `output_schema` if the function changes the data structure.")
        else:
            logger.debug(f"Using output schema for step {func_name}: {final_output_schema.names}")
        
        operation_details = {
            "user_func": user_func, 
            "args": args, 
            "kwargs": kwargs, 
            "distributed_hint": distributed, 
            "func_name": func_name
        }
        
        return GeoImageCollection(
            operation_type="APPLY_FUNC", 
            operation_details=operation_details, 
            expected_output_schema=final_output_schema, 
            parent_node=self, 
            http_client=self._http_client, 
            metadata_client=self._metadata_client, 
            should_close_http_client=False, 
            window_limit=self._window_limit,
            processing_engine_grpc_target=self._grpc_target,
            processing_engine_flight_target=self._flight_target
        )


    # --- Build Protobuf Plan ---
    def _build_workflow_plan(self) -> processing_engine_pb2.WorkflowPlan:
        """
        Build the workflow plan for execution.
        
        This method constructs a WorkflowPlan protobuf message that represents
        the entire pipeline of operations to be executed.
        
        Returns:
            A WorkflowPlan protobuf message
        """
        plan = processing_engine_pb2.WorkflowPlan()
        has_load_step = False
        current_limit = 0

        # Process steps from load to current node
        for step in self._pipeline_steps_from_load:
            op_type = step["operation_type"]
            
            if op_type == "LOAD":
                if has_load_step: continue # Only use the first LOAD step
                has_load_step = True
                
                # Get the operation details
                op_details = step["operation_details"]
                
                # Set load step parameters
                plan.load_step.collection = str(op_details["collection"]) if op_details.get("collection") else ""
                plan.load_step.bands.extend(str(band) for band in (op_details["bands"] or []))
                
                # Handle AOI information
                if "aoi" in op_details:
                    plan.load_step.aoi_wkt = str(op_details["aoi"]["wkt"]) if op_details["aoi"].get("wkt") else ""
                    plan.load_step.aoi_crs = str(op_details["aoi"]["crs"]) if op_details["aoi"].get("crs") else ""
                else:
                    plan.load_step.aoi_wkt = str(op_details.get("aoi_wkt", ""))
                    plan.load_step.aoi_crs = str(op_details.get("aoi_crs", ""))
                
                # Handle datetime filter
                plan.load_step.datetime_filter = str(op_details.get("datetime", ""))
                
                # Handle scene limit
                plan.load_step.scene_limit = int(op_details.get("limit", 0))
                
                # Handle catalog identifier
                plan.load_step.catalog_identifier = str(op_details.get("catalog_identifier", ""))
                
                # Handle filters
                filters = op_details.get("filters", {})
                if filters:
                    prop_struct = Struct()
                    string_filters = {k: str(v) for k, v in filters.items()}
                    prop_struct.update(string_filters)
                    plan.load_step.property_filters.CopyFrom(prop_struct)
                    
                # Add spatial window limit if present
                if "spatial_window_limit" in op_details:
                    plan.load_step.spatial_window_limit = int(op_details["spatial_window_limit"])
                    logger.debug(f"Setting spatial window limit in LOAD step: {op_details['spatial_window_limit']}")
            elif op_type == "APPLY_FUNC":
                apply_func = plan.apply_steps.add()
                function_id = step["operation_details"].get("function_id", step["operation_details"].get("func_name", "unknown"))
                apply_func.function_id = function_id
                params_dict = {}
                if step["operation_details"].get("kwargs"): params_dict.update(step["operation_details"]["kwargs"])
                params_struct = Struct()
                if params_dict: params_struct.update(params_dict)
                apply_func.parameters.CopyFrom(params_struct)
            elif op_type == "HEAD_LIMIT":
                # The _window_limit on the *final* node holds the effective limit
                current_limit = step["operation_details"].get("n", 0)
                # Store the spatial window limit if present
                if "spatial_window_limit" in step["operation_details"]:
                    plan.head_limit = int(step["operation_details"]["spatial_window_limit"])
                    logger.debug(f"Setting head_limit from HEAD_LIMIT step: {step['operation_details']['spatial_window_limit']}")
                else:
                    plan.head_limit = self._window_limit or 0

        if not has_load_step: raise ValueError("Invalid GeoImageCollection: No LOAD step.")
        
        # Log the final plan
        logger.info(f"Built workflow plan with head_limit={plan.head_limit}")
        return plan

    def head(self, n_windows: int = 5) -> 'GeoImageCollection':
        """
        Limit the number of unique spatial windows in the collection.
        
        Args:
            n_windows: Number of unique spatial windows to keep
            
        Returns:
            A new GeoImageCollection with the head limit applied
        """
        if not isinstance(n_windows, int) or n_windows <= 0:
            raise ValueError("n_windows must be positive.")
        logger.info(f"Adding HEAD_LIMIT step: n={n_windows}")
        effective_limit = min(self._window_limit, n_windows) if self._window_limit is not None else n_windows
        
        # Create operation details with window limit
        operation_details = {
            "n": n_windows,
            "spatial_window_limit": effective_limit  # Add explicit spatial window limit
        }
        
        # Create a new node with the head limit
        head_node = GeoImageCollection(
            operation_type="HEAD_LIMIT", 
            operation_details=operation_details, 
            expected_output_schema=self._expected_output_schema, 
            parent_node=self, 
            http_client=self._http_client, 
            metadata_client=self._metadata_client, 
            should_close_http_client=False, 
            window_limit=effective_limit,
            processing_engine_grpc_target=self._grpc_target,
            processing_engine_flight_target=self._flight_target
        )
        
        # If this is being applied after an APPLY_FUNC operation, we need to ensure
        # the head limit is propagated to the input data
        if self._operation_type == "APPLY_FUNC":
            # Get the input node (the node before APPLY_FUNC)
            input_node = self._parent_node
            if input_node:
                # Create a new head node for the input data with the same limit
                input_head_node = input_node.head(effective_limit)
                # Update the parent chain
                head_node._parent_node = input_head_node
                
        return head_node

    async def _compute_async(self) -> pa.Table:
        """
        Execute the graph and return a PyArrow Table with the results.
        
        This method builds a workflow plan from the pipeline, sends an ExecuteWorkflowRequest
        to the processing engine via gRPC, and fetches the results via Arrow Flight.
        """
        # First, get a unique job ID for this execution
        job_id = f"job_{uuid4().hex}"
        logger.info(f"Executing workflow with job_id: {job_id}")
        
        try:
            # Build the workflow plan from the pipeline
            plan = self._build_workflow_plan()
            
            # Create the gRPC request
            request = processing_engine_pb2.ExecuteWorkflowRequest(
                job_id=job_id,
                plan=plan,
                execution_mode=processing_engine_pb2.ExecuteWorkflowRequest.ExecutionMode.RAY
            )
            
            # Get the gRPC channel and make the call
            channel = self._get_grpc_channel()
            stub = processing_engine_pb2_grpc.ProcessingEngineServiceStub(channel)
            
            # Execute the workflow via gRPC
            logger.info(f"Sending ExecuteWorkflow request to {self._grpc_target}")
            response = await stub.ExecuteWorkflow(request)
            logger.info(f"Received ExecuteWorkflow response: {response}")
            
            # The response contains a Flight ticket for retrieving the results
            flight_ticket = response.flight_ticket
            if not flight_ticket:
                raise ValueError("No Flight ticket received in response")
                
            # Parse the flight endpoint from the target
            from urllib.parse import urlparse
            flight_url = urlparse(self._flight_target)
            flight_host = flight_url.hostname
            flight_port = flight_url.port or 50052  # Default Flight port
            
            # Create Flight client
            client = fl.FlightClient(f"grpc+tcp://{flight_host}:{flight_port}")
            logger.info(f"Connecting to Flight server at {flight_host}:{flight_port}")
            
            # Create the Flight ticket
            ticket = fl.Ticket(flight_ticket.encode('utf-8'))
            
            # Poll for readiness with retries
            max_retries = 60  # 60 seconds max wait time
            retry_delay = 1.0  # 1 second between retries
            
            for attempt in range(max_retries):
                try:
                    # Try to get the reader
                    reader = client.do_get(ticket)
                    
                    # Read all data at once
                    table = reader.read_all()
                    if table.num_rows > 0:
                        logger.info(f"Received result table with {table.num_rows} rows and {len(table.schema.names)} columns")
                    else:
                        logger.warning("Received empty result set from Flight server")
                    break
                        
                except fl.FlightUnavailableError:
                    if attempt < max_retries - 1:
                        logger.info(
                            f"Workflow {job_id} still in progress "
                            f"(attempt {attempt + 1}/{max_retries}), retrying in {retry_delay}s"
                        )
                        await asyncio.sleep(retry_delay)
                    else:
                        raise TimeoutError(
                            f"Workflow {job_id} did not complete after "
                            f"{max_retries} attempts ({max_retries * retry_delay}s)"
                        )

            # If we have a window limit, filter the final output to ensure we only get the requested number of unique chunks
            if self._window_limit is not None:
                table = self._filter_unique_chunks(table)
                logger.info(f"Filtered table to {table.num_rows} unique chunks")

            return table

        except Exception as e:
            logger.error(f"Error executing workflow: {str(e)}")
            raise

    def _filter_unique_chunks(self, table: pa.Table) -> pa.Table:
        """
        Filter the table to keep only the first N unique spatial chunks.
        
        Args:
            table: The input PyArrow Table containing raster data
            
        Returns:
            A new Table containing only the first N unique spatial chunks
        """
        # Sort by chunk_id to ensure consistent ordering
        sorted_table = table.sort_by([("chunk_id", "ascending")])
        
        # Take the first N rows based on the window_limit
        return sorted_table.slice(0, self._window_limit)

    async def _iter_batches_async(self) -> AsyncIterator[pa.RecordBatch]:
        """
        Execute the graph and yield RecordBatches with the results.
        
        This method is similar to _compute_async but yields batches incrementally
        instead of returning a complete table.
        """
        # First, get a unique job ID for this execution
        job_id = f"job_{uuid4().hex}"
        logger.info(f"Executing workflow with job_id: {job_id} (iterative mode)")
        
        try:
            # Build the workflow plan from the pipeline
            plan = self._build_workflow_plan()
            
            # Create the gRPC request
            request = processing_engine_pb2.ExecuteWorkflowRequest(
                job_id=job_id,
                plan=plan,
                execution_mode=processing_engine_pb2.ExecuteWorkflowRequest.ExecutionMode.RAY
            )
            
            # Get the gRPC channel and make the call
            channel = self._get_grpc_channel()
            stub = processing_engine_pb2_grpc.ProcessingEngineServiceStub(channel)
            
            # Execute the workflow via gRPC
            logger.info(f"Sending ExecuteWorkflow request to {self._grpc_target} (streaming)")
            response = await stub.ExecuteWorkflow(request)
            logger.info(f"Received ExecuteWorkflow response: {response}")
            
            # The response contains a Flight ticket for retrieving the results
            flight_ticket = response.flight_ticket
            if not flight_ticket:
                raise ValueError("No Flight ticket received in response")
                
            # Get the Flight client and fetch the results
            client = self._get_flight_client()
            logger.info(f"Fetching streaming results from Flight server using ticket: {flight_ticket}")
            
            # Convert the ticket string to a Flight ticket object
            ticket = fl.Ticket(flight_ticket.encode())
            
            # Fetch the results as a reader and yield batches
            reader = client.do_get(ticket)
            for batch in reader:
                logger.debug(f"Received batch with {batch.num_rows} rows")
                yield batch
                        
        except Exception as e:
            logger.error(f"Error executing workflow in streaming mode: {str(e)}")
            raise

    def compute(self) -> pa.Table:
        """
        Execute the graph and return a PyArrow Table with the results.
        """
        # This is a synchronous wrapper around _compute_async
        return asyncio.run(self._compute_async())

    def iter_batches(self):
        """
        Execute the graph and yield RecordBatches with the results.
        """
        # This is a synchronous wrapper around _iter_batches_async
        async def async_iter():
            async for batch in self._iter_batches_async():
                yield batch
                
        def sync_iter():
            for batch in asyncio.run(async_iter()):
                        yield batch
                
        return sync_iter()

    def _get_load_node(self) -> 'GeoImageCollection': 
        """Get the LOAD node at the start of the pipeline."""
        curr = self
        while curr is not None:
            if curr._operation_type == "LOAD":
                return curr
            curr = curr._parent_node
        raise ValueError("No LOAD node found in pipeline")

    def _infer_output_schema(self, func: Callable) -> Optional[pa.Schema]: return None 

    def _determine_backend(self) -> str: 
        """Determine which backend to use for execution."""
        # For now, always use Ray
        return "ray"

    def _get_node_by_func_name(self, func_name: str) -> Optional['GeoImageCollection']: 
        """Find a node in the pipeline by function name."""
        curr = self
        while curr is not None:
            if curr._operation_type == "APPLY_FUNC" and curr._operation_details.get("func_name") == func_name:
                return curr
            curr = curr._parent_node
        return None

    def _get_grpc_channel(self): # Basic insecure channel
        return grpc_async.insecure_channel(self._grpc_target)

    def _get_flight_client(self): # Basic insecure client
        return fl.FlightClient(self._flight_target)