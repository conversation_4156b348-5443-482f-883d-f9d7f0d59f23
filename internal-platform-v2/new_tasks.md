Here’s an updated **new\_tasks.md** that:

* Removes any “integration with driver’s cache writes” leftovers
* Refines the Flight‐server tasks to focus on stale‐cache eviction/TTL and proper retry mechanism
* Keeps the SDK subtasks for streaming batches and full gRPC+Flight integration
* Adds a small CLI/onboarding housekeeping item under Docs

---

# new\_tasks.md

This document tracks macro- and micro-level tasks across the codebase. Each entry has a status tag: `[DONE]`, `[IN PROGRESS]`, `[TODO]`, or `[PENDING]`.

---

## 1. Core Executor Refactor (libs/tfw\_engine\_core/runtime\_ray/)

* **\[DONE] 1.1 Define `WindowSpec` Dataclass**

  * Created `common_types.py` with `@dataclass WindowSpec`
  * Verified serialization/deserialization in tests
  * Updated import paths in `planner.py` and `driver.py`

* **\[DONE] 1.2 Implement `planner.py`**

  * Queries `CatalogClient` for STAC assets
  * Generates spatial windows via `grid.py` subdivision
  * Applies head limits correctly
  * Added unit tests with mocked catalog responses

* **\[DONE] 1.3 Refactor `worker.py`**

  * Converted `process_batch_on_worker` into `@ray.remote` task
  * Fetches COGs, applies `process.py` kernels end-to-end
  * Verified local execution without Ray via direct calls
  * Added error-handling for HTTP timeouts and COG fetch

* **\[DONE] 1.4 Implement `driver.py`**
  *Goal:* Orchestrate end-to-end flow: planner → Ray tasks → aggregate → cache

  * **Completed:**

    1. Hooked up `RayDriver` to call `planner.generate_windows()`
    2. Submits each `WindowSpec` as a Ray task with execution_id parameter
    3. Collects returned `pyarrow.RecordBatch` futures
    4. **Fixed worker-to-Flight server communication**
       * Workers now successfully upload results via synchronous `do_put`
       * Driver signals completion to Flight server
    5. **Assemble final `pa.Table`**
       * Results properly aggregated and cached in Flight server
    6. **Error propagation**
       * Status tracking works correctly through Flight server cache

---

## 2. Kernel Registry Refinement

* **\[DONE] 2.1 Modularize Kernels**

  * Moved `_ndvi_kernel` into `kernels/vegetation.py`
  * Renamed functions and updated imports

* **\[DONE] 2.2 Package Kernels**

  * Created `kernels/__init__.py`
  * Exposed submodules: `vegetation`, `filters`, etc.

* **\[DONE] 2.3 Ensure Registry Imports**

  * Updated `worker.py` to `import terrafloww.engine_core.kernels` before lookup
  * Added smoke test to verify registry finds each kernel

---

## 3. Local Service Setup & Updates

* **\[DONE] 3.1 Update `grpc_service.py`**

  * Switched service to use `RayDriver` instead of legacy executor
  * Initialized Ray with `ray.init(ignore_reinit_error=True)`
  * Validated gRPC requests trigger Ray workflow

* **\[DONE] 3.2 Enhance `flight_server.py`**
  *Goal:* Serve workflow results reliably via Arrow Flight

  * **Completed:**

    1. `do_get` reads status & table from shared caches
    2. Creates placeholder on first request to avoid KeyError
    3. **Fixed `do_put` method for worker uploads**
       * Changed from async to synchronous to match PyArrow Flight expectations
       * Workers now successfully upload results via `do_put`
       * Driver completion signals work correctly
    4. **Retry mechanism**
       * Return `FlightUnavailableError` with "not complete yet" until `status=="COMPLETED"`
       * SDK retry mechanism works correctly with proper error messages
  * **Remaining subtasks:**

    1. **Stale-cache eviction / TTL**
       * Expire old cache entries after configurable TTL
       * Optionally trigger manual cleanup API

* **\[DONE] 3.3 Local Run Script**

  * Created `services/processing_engine/app/main.py`
  * Launches gRPC + Flight endpoints in one process
  * Supports `HOST`/`PORT` via environment variables
  * Added Dockerfile and `docker-compose.yml` for local stacks

---

## 4. SDK Implementation (Thin Client)

* **\[DONE] 4.1 Refactor `sdk.load()`**

  * Returns a `GeoImageCollection` in `sdk/tesseract/collections.py`
  * Chains `.filter()`, `.apply()`, `.head()` to build plan

* **\[DONE] 4.2 Implement `_build_workflow_plan()`**

  * Translates chained calls into Protobuf `WorkflowPlan`
  * Added unit tests comparing plan messages against fixtures

* **\[DONE] 4.3 Implement `_compute_async()` (Basic Operations Only)**
  *Goal:* Submit plan via gRPC and fetch results over Flight in one seamless flow

  * **Completed:**

    1. **gRPC call** (`ExecuteWorkflow`) → `execution_id` + `flight_ticket`
    2. **Polling** Flight server until ready (with retry mechanism)
    3. **Flight client** (`client.do_get(ticket)`) → `FlightStreamReader`
    4. **End-to-end integration**: glue gRPC + Flight calls in `_compute_async()`
    5. **Assemble result**: `pa.concat_tables(batches)` → final `pa.Table`
    6. **Expose to user**: return `Table` (or wrap in `GeoImageCollectionResult`)
    7. **Fixed Flight server `do_put` method**
       * Changed from async to synchronous to match PyArrow Flight expectations
       * Workers now successfully upload results and driver signals completion
    8. **Working operations**: `load()`, `apply()` (prebuilt kernels), `head()`, `compute()`

  * **Current Limitations:**
    * ❌ `.filter()` method not implemented (AOI, date range filtering)
    * ❌ Custom user functions not fully tested
    * ✅ Only prebuilt server-side kernels (e.g., NDVI) work reliably

* **\[TODO] 4.4 Implement `filter()` method**
  *Goal:* Add spatial and temporal filtering capabilities to GeoImageCollection

  * **Subtasks:**

    1. **Implement `filter()` method in `collections.py`**
       * Support AOI filtering (WKT, GeoJSON, Shapely geometries)
       * Support date range filtering (ISO 8601 format)
       * Support cloud cover and other metadata filters
    2. **Update workflow plan builder**
       * Add filter steps to protobuf WorkflowPlan
       * Integrate with existing planner logic
    3. **Server-side filter processing**
       * Update planner to apply filters when generating windows
       * Ensure filtered results work with existing kernels
    4. **Unit tests**
       * Test filter method with various AOI formats
       * Test date range filtering
       * Test combined filters with apply operations

* **\[TODO] 4.5 Implement `iter_batches()`**
  *Goal:* Stream results lazily rather than full materialization

  * **Subtasks:**

    1. Async generator in `collections.py`
    2. Use Flight reader APIs to yield each `RecordBatch`
    3. Support user cancellation / early Stop
    4. Unit tests mocking multi-batch streams

---

## 5. Testing (Local End-to-End)

* **\[DONE] 5.1 Unit Tests**

  * Coverage for: planner, worker, driver *logic up to Ray submission*
  * Plan-builder and registry tests

* **\[DONE] 5.2 Local Integration Test (Basic Operations)**

  * Script ingests a small STAC catalog into Delta Lake
  * Starts Ray, gRPC + Flight servers via `main.py`
  * Invokes `collection.compute()` in SDK and asserts shape
  * **Working**: `load()` → `apply()` (prebuilt kernels) → `head()` → `compute()`
  * **Not working**: Tests with `.filter()` method fail (not implemented)

* **\[TODO] 5.3 Fix Failing Filter Tests**
  *Goal:* Make `test_geoimagecollection_operations.py` pass

  * **Current Issue:**
    * `test_geoimagecollection_operations.py` fails with `AttributeError: 'GeoImageCollection' object has no attribute 'filter'`
    * Test tries to use `.filter(aoi=..., date_range=...)` which is not implemented

  * **Subtasks:**
    1. Implement `filter()` method (see task 4.4)
    2. Update test to work with current limitations or implement missing functionality
    3. Ensure all SDK tests pass

* **\[TODO] 5.4 End-to-End Flight/SDK Integration Test**
  *Goal:* Validate Flight server and SDK client interop beyond SDK helper

  * **Subtasks:**

    1. Native `pyarrow.flight.FlightClient.get_flight_info` + `do_get` → table
    2. Assert matching schema/row count
    3. Simulate partial failures & retries

---

## 🚀 **HIGH PRIORITY: Kubernetes Deployment Readiness**

* **\[TODO] 6.1 KubeRay Setup and Configuration**
  *Goal:* Deploy Ray cluster on Kubernetes using KubeRay operator

  * **Subtasks:**

    1. **Install KubeRay Operator**
       * Create Helm chart for KubeRay operator installation
       * Configure RBAC permissions and CRDs
       * Set up operator namespace and service accounts
    2. **Ray Cluster Configuration**
       * Create RayCluster custom resource manifests
       * Configure head node with dashboard and GCS server
       * Configure worker nodes with auto-scaling
       * Set resource limits (CPU, memory, GPU if needed)
    3. **Ray Image Preparation**
       * Create custom Ray Docker image with platform dependencies
       * Include all required Python packages (pyarrow, rasterio, etc.)
       * Pre-install platform libraries (tfw-engine-core, etc.)
    4. **Ray Service Configuration**
       * Expose Ray dashboard via Kubernetes service
       * Configure Ray client access from processing engine
       * Set up proper networking and service discovery

* **\[TODO] 6.2 Processing Engine Kubernetes Deployment**
  *Goal:* Deploy processing engine as scalable Kubernetes service

  * **Subtasks:**

    1. **Improve Dockerfile**
       * Update base image to Python 3.12 (current uses 3.9)
       * Multi-stage build for smaller image size
       * Proper dependency caching and layer optimization
       * Include all platform libraries in image
    2. **Kubernetes Manifests**
       * Deployment manifest with proper resource limits
       * Service manifest for gRPC (50051) and Flight (50052) ports
       * ConfigMap for environment variables
       * Secret management for sensitive configuration
    3. **Service Configuration**
       * Health checks and readiness probes
       * Horizontal Pod Autoscaler (HPA) configuration
       * Resource requests and limits
       * Anti-affinity rules for high availability
    4. **Environment Variables**
       * RAY_ADDRESS pointing to Ray cluster service
       * FLIGHT_HOST and FLIGHT_PORT configuration
       * Logging and monitoring configuration

* **\[TODO] 6.3 Helm Chart Development**
  *Goal:* Create comprehensive Helm chart for easy deployment

  * **Subtasks:**

    1. **Chart Structure**
       * Create helm chart directory structure
       * Chart.yaml with proper metadata and dependencies
       * values.yaml with all configurable parameters
       * Templates for all Kubernetes resources
    2. **Component Charts**
       * KubeRay operator as dependency
       * Ray cluster configuration
       * Processing engine deployment
       * Ingress configuration for external access
    3. **Configuration Management**
       * Environment-specific values files (dev, staging, prod)
       * Secret management integration
       * Resource scaling parameters
       * Monitoring and logging configuration
    4. **Dependencies**
       * KubeRay operator Helm chart dependency
       * Optional monitoring stack (Prometheus, Grafana)
       * Optional ingress controller dependency

* **\[TODO] 6.4 Container Image Optimization**
  *Goal:* Create production-ready container images

  * **Subtasks:**

    1. **Processing Engine Image**
       * Multi-stage Dockerfile for smaller size
       * Security scanning and vulnerability fixes
       * Non-root user configuration
       * Proper signal handling for graceful shutdown
    2. **Ray Worker Image**
       * Custom Ray image with platform dependencies
       * Include all required geospatial libraries
       * Optimize for fast startup and scaling
       * Include monitoring and logging agents
    3. **Image Registry Setup**
       * Configure container registry (Docker Hub, ECR, GCR)
       * Set up CI/CD pipeline for image building
       * Image versioning and tagging strategy
       * Security scanning integration

* **\[TODO] 6.5 Service Discovery and Networking**
  *Goal:* Configure proper service communication in Kubernetes

  * **Subtasks:**

    1. **Service Mesh (Optional)**
       * Evaluate Istio/Linkerd for service communication
       * Configure traffic policies and security
       * Set up observability and tracing
    2. **DNS and Service Discovery**
       * Configure Kubernetes DNS for service discovery
       * Set up proper service naming conventions
       * Configure load balancing for Ray workers
    3. **Network Policies**
       * Define network policies for security
       * Restrict inter-pod communication
       * Configure ingress and egress rules
    4. **Ingress Configuration**
       * Set up ingress controller (NGINX, Traefik)
       * Configure TLS termination
       * Set up external access to services

* **\[TODO] 6.6 Monitoring and Observability**
  *Goal:* Set up comprehensive monitoring for production

  * **Subtasks:**

    1. **Metrics Collection**
       * Prometheus integration for metrics
       * Ray dashboard metrics exposure
       * Custom application metrics
       * Resource utilization monitoring
    2. **Logging**
       * Centralized logging with Fluentd/Fluent Bit
       * Log aggregation and parsing
       * Structured logging format
       * Log retention policies
    3. **Tracing**
       * Distributed tracing with Jaeger/Zipkin
       * Request flow tracking
       * Performance bottleneck identification
    4. **Alerting**
       * Alertmanager configuration
       * Critical system alerts
       * Performance degradation alerts
       * Capacity planning alerts

* **\[TODO] 6.7 Security and Compliance**
  *Goal:* Implement security best practices

  * **Subtasks:**

    1. **Pod Security**
       * Pod Security Standards implementation
       * Security contexts and capabilities
       * Non-root container execution
       * Read-only root filesystems where possible
    2. **RBAC Configuration**
       * Service account creation
       * Role-based access control
       * Principle of least privilege
       * Namespace isolation
    3. **Secret Management**
       * Kubernetes secrets for sensitive data
       * External secret management integration
       * Secret rotation policies
       * Encryption at rest
    4. **Network Security**
       * Network policies for pod communication
       * TLS encryption for all communications
       * Certificate management
       * Firewall rules and security groups

* **\[TODO] 6.8 Storage and Persistence**
  *Goal:* Configure persistent storage for stateful components

  * **Subtasks:**

    1. **Persistent Volumes**
       * Storage classes configuration
       * Persistent volume claims for Ray
       * Backup and restore procedures
       * Storage performance optimization
    2. **Data Management**
       * Configure data persistence for Ray object store
       * Set up shared storage for large datasets
       * Implement data lifecycle policies
       * Configure data backup strategies
    3. **Cache Management**
       * Redis/Memcached for distributed caching
       * Cache persistence and recovery
       * Cache eviction policies
       * Performance monitoring

* **\[TODO] 6.9 CI/CD Pipeline Integration**
  *Goal:* Automate deployment and updates

  * **Subtasks:**

    1. **Build Pipeline**
       * Automated Docker image building
       * Multi-architecture image support
       * Security scanning integration
       * Image vulnerability assessment
    2. **Deployment Pipeline**
       * GitOps workflow with ArgoCD/Flux
       * Environment promotion strategy
       * Rollback procedures
       * Blue-green deployment support
    3. **Testing Integration**
       * Integration tests in Kubernetes
       * End-to-end testing pipeline
       * Performance testing automation
       * Chaos engineering tests
    4. **Release Management**
       * Semantic versioning strategy
       * Release notes automation
       * Deployment approval workflows
       * Monitoring deployment health

* **\[TODO] 6.10 Documentation and Operations**
  *Goal:* Create comprehensive operational documentation

  * **Subtasks:**

    1. **Deployment Guide**
       * Step-by-step installation instructions
       * Prerequisites and requirements
       * Configuration examples
       * Troubleshooting guide
    2. **Operations Runbook**
       * Common operational procedures
       * Scaling and capacity planning
       * Backup and recovery procedures
       * Incident response playbooks
    3. **Architecture Documentation**
       * System architecture diagrams
       * Component interaction flows
       * Security architecture
       * Performance characteristics
    4. **API Documentation**
       * gRPC API documentation
       * Flight API documentation
       * SDK usage examples
       * Integration patterns

---

## 6. Documentation & Onboarding

* **\[PENDING] 6.1 Improve `README.md`**

  * **Needs:**

    * Step-by-step developer setup (STAC ingestion, env vars, Ray init)
    * CLI commands to ingest data, launch services & run tests
    * Quickstarts: `.filter()`, `.apply()`, `.compute()`, `.iter_batches()`

* **\[PENDING] 6.2 Developer Onboarding Guide**

  * One-pager covering:

    * Repo structure and module responsibilities
    * Adding a new kernel
    * Running & debugging Flight server
    * Extending SDK

* **\[PENDING] 6.3 Monitoring & CI/CD**

  * Add GitHub Actions:

    * Linting, static types, unit tests
    * Docker integration: spin up services, run integration tests
  * Alerting for cache bloat, test flakiness

---

*Last updated: 2025-06-16*
