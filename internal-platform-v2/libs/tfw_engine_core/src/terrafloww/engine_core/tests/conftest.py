# libs/tfw_engine_core/tests/conftest.py
import sys
import os
from pathlib import Path

# Calculate the path to the monorepo root directory
# This assumes conftest.py is in libs/tfw_engine_core/tests/
# Adjust the number of .parent calls if the location differs
_MONOREPO_ROOT = Path(__file__).parent.parent.parent.parent.parent.parent.parent
_SERVICES_DIR = _MONOREPO_ROOT / "services"
print(f"\n[conftest.py] Monorepo root: {_MONOREPO_ROOT}")
print(f"\n[conftest.py] Services dir: {_SERVICES_DIR}")

# Add the 'services' directory to sys.path for the duration of the tests
# This allows tests within this directory structure to import from services.*
# Note: This affects all tests run under this conftest.
if str(_SERVICES_DIR) not in sys.path:
    print(f"\n[conftest.py] Adding to sys.path: {str(_SERVICES_DIR)}")
    sys.path.insert(0, str(_SERVICES_DIR)) # Insert at beginning for precedence
else:
    print(f"\n[conftest.py] Already in sys.path: {str(_SERVICES_DIR)}")

# You can also define fixtures here if needed later