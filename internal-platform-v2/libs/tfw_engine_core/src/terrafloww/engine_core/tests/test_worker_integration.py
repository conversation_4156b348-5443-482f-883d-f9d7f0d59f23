# libs/tfw_engine_core/tests/runtime_ray/test_worker_integration.py

import pytest
import pyarrow as pa
import asyncio
import os
from datetime import datetime
from shapely.geometry import box
from collections import defaultdict
import logging

# Make sure Ray is available and initialized
import ray
if not ray.is_initialized():
    # Initialize Ray locally for the test module
    # Adjust resources as needed for local testing
    ray.init(ignore_reinit_error=True, num_cpus=4)

# Import the target module and types
from terrafloww.engine_core.runtime_ray import planner
from terrafloww.engine_core.runtime_ray import worker # Import the worker module itself
from terrafloww.engine_core.runtime_ray.common_types import WindowSpec
from terrafloww.processing_engine.v1 import processing_engine_pb2
from tfw_raster_schemas.raster import RASTER_CHUNK_SCHEMA

# Import helpers from planner tests (or redefine simplified ones)
from .test_planner_unit import (
    create_test_workflow_plan,
    TEST_COLLECTION, TEST_BAND_RED, TEST_BAND_NIR, TEST_AOI_WKT, TEST_CRS,
    # We won't use the mock table helpers here
)

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s", stream=sys.stdout
)
logger = logging.getLogger(__name__)

# --- Test Configuration ---
# Ensure this path points to a valid Delta table created by ingest_ext_stac.py
# Use the small NYC dataset ingested previously.
CATALOG_PATH = os.environ.get("TFW_EXTERNAL_CATALOG_PATH", "/tmp/platform_delta_tables/ext_stac_datasets")
# Use the same AOI/Time as the ingest script used for the test data
TEST_INGEST_AOI = box(-74.05, 40.75, -73.95, 40.85).wkt # Matches test_dsl_compute AOI
TEST_INGEST_DATETIME = "2024-03-01T00:00:00Z/2024-03-03T23:59:59Z" # Matches test_dsl_compute


@pytest.fixture(scope="module", autouse=True)
def ensure_catalog_path():
    """Fixture to check if the test catalog path seems valid."""
    if not os.path.exists(os.path.join(CATALOG_PATH, "_delta_log")):
        pytest.skip(f"Test catalog not found at {CATALOG_PATH}. Run ingest_ext_stac.py first.")
    # Set environment variable for the planner to use
    os.environ["TFW_EXTERNAL_CATALOG_PATH"] = CATALOG_PATH


@pytest.mark.asyncio
async def test_worker_integration_fetch_decode_stack_ndvi(mocker):
    """
    Integration test: Plan using real catalog, execute worker via local Ray.
    Tests fetch, decode, stacking, and NDVI kernel application.
    """
    # --- 1. Plan Execution to get realistic WindowSpecs ---
    test_plan = create_test_workflow_plan(
        collection="sentinel-2-l2a", # Use the actual collection name used for ingest
        bands=[TEST_BAND_RED, TEST_BAND_NIR], # Request bands needed for NDVI
        aoi_wkt=TEST_INGEST_AOI,
        aoi_crs="EPSG:4326", # CRS of the AOI WKT
        datetime_filter=TEST_INGEST_DATETIME,
        head_limit=0, # No head limit for planning phase of this test
        apply_steps=[{"function_id": "ndvi", "parameters": {}}] # Add NDVI step
    )

    print(f"\nPlanning execution for AOI: {TEST_INGEST_AOI}, Time: {TEST_INGEST_DATETIME}")

    planned_specs = []
    # --- MODIFIED Logic: Collect specs for N chunks ---
    target_num_chunks = 1 # Let's aim to get all bands for just ONE chunk for this test
    collected_chunks_keys = set()
    collected_specs_for_target_chunks = []

    try:
        async for spec in planner.plan_execution(test_plan):
            spatial_key = (spec.scene_id, spec.tile_r, spec.tile_c)

            # If we haven't found enough target chunks yet...
            if len(collected_chunks_keys) < target_num_chunks:
                # Add this chunk's key if it's new
                collected_chunks_keys.add(spatial_key)
                # Add the spec regardless, as it might be for one of our target chunks
                collected_specs_for_target_chunks.append(spec)
            # If this spec belongs to a chunk we've already targeted...
            elif spatial_key in collected_chunks_keys:
                 # Add this spec as well (it might be the other band we need)
                 collected_specs_for_target_chunks.append(spec)
            # Else: We have enough chunks, and this spec is for a new chunk we don't need for the test. Ignore it.

            # Optional safety break if planner yields unexpectedly large number of specs
            # for the target chunks (shouldn't happen with 2 bands)
            if len(collected_specs_for_target_chunks) > 10: # Arbitrary limit
                 print("Collected >10 specs, stopping planner for safety.")
                 break

        # Replace planned_specs with the filtered list
        planned_specs = collected_specs_for_target_chunks
        print(f"Planner yielded specs. Collected {len(planned_specs)} specs covering {len(collected_chunks_keys)} target spatial chunk(s).")

    except Exception as e:
        pytest.fail(f"Planner execution failed during test setup: {e}")

    assert len(planned_specs) > 0, "Planner did not generate any WindowSpecs for the targeted chunks."

    # --- Group collected specs (same as before) ---
    grouped_by_chunk = defaultdict(list)
    for spec in planned_specs:
        key = (spec.scene_id, spec.tile_r, spec.tile_c)
        grouped_by_chunk[key].append(spec)

    # --- Select batch (same as before) ---
    selected_specs_batch = None
    for key, specs_in_group in grouped_by_chunk.items():
        bands_in_group = {s.band_name for s in specs_in_group}
        if TEST_BAND_RED in bands_in_group and TEST_BAND_NIR in bands_in_group:
            # Make sure we only take the red and nir specs for the test batch
            selected_specs_batch = [s for s in specs_in_group if s.band_name in (TEST_BAND_RED, TEST_BAND_NIR)]
            print(f"Selected specs for chunk: {key}, Bands: {[s.band_name for s in selected_specs_batch]}")
            break # Found a complete chunk

    assert selected_specs_batch is not None, f"Could not find a spatial chunk with both {TEST_BAND_RED} and {TEST_BAND_NIR} specs from planner."
    assert len(selected_specs_batch) >= 2 # Should have at least red and nir


    # --- 2. Define Apply Steps ---
    test_apply_steps = [{"function_id": "ndvi", "parameters": {}}]

    # --- 3. Execute Worker Task via Ray ---
    print(f"Submitting {len(selected_specs_batch)} specs to worker task...")
    
    # Ensure the worker code uses the correct function reference
    worker_future = worker.process_batch_on_worker.remote(selected_specs_batch, test_apply_steps)

    result_batch = None
    try:
        # Use asyncio.to_thread to avoid blocking the event loop with ray.get
        # Increase timeout as fetching/processing real data can take time
        result_batch = await asyncio.to_thread(ray.get, worker_future, timeout=180)
        print("Worker task completed.")
    except Exception as e:
        pytest.fail(f"Ray worker task failed or timed out: {e}")

    # --- 4. Assertions ---
    assert result_batch is not None, "Worker returned None"
    assert isinstance(result_batch, pa.RecordBatch), f"Worker returned type {type(result_batch)}, expected RecordBatch"
    print(f"Worker returned batch with {result_batch.num_rows} rows and schema:\n{result_batch.schema}")

    assert result_batch.num_rows == 1, f"Expected 1 output row (one spatial chunk), got {result_batch.num_rows}"

    # Verify base schema + NDVI column
    expected_col_names = set(RASTER_CHUNK_SCHEMA.names) | {'ndvi'} # Expect ndvi column added
    actual_col_names = set(result_batch.schema.names)
    assert actual_col_names == expected_col_names, f"Schema mismatch. Expected ~{expected_col_names}, Got {actual_col_names}"

    # Inspect the first (only) row
    row = result_batch.to_pylist()[0]

    # Check combined bands list (should be sorted alphabetically by worker logic)
    assert sorted(row['bands']) == sorted([TEST_BAND_NIR, TEST_BAND_RED]), f"Expected bands ['nir', 'red'], got {row['bands']}"
    num_bands = len(row['bands'])
    assert num_bands == 2

    # Check shape [C, H, W]
    assert isinstance(row['shape'], list) and len(row['shape']) == 3, f"Expected shape [C,H,W], got {row['shape']}"
    assert row['shape'][0] == num_bands
    height, width = row['shape'][1], row['shape'][2]
    assert height > 0 and width > 0
    # Compare shape to reference spec (can be slightly different due to windowing)
    ref_spec = selected_specs_batch[0]
    assert height == ref_spec.window_height
    assert width == ref_spec.window_width

    # Check raster_data length
    assert isinstance(row['raster_data'], list), "raster_data should be a flat list"
    expected_len = num_bands * height * width
    assert len(row['raster_data']) == expected_len, f"Expected {expected_len} elements in raster_data, got {len(row['raster_data'])}"

    # Check NDVI column
    assert 'ndvi' in row, "NDVI column is missing"
    assert isinstance(row['ndvi'], list), "NDVI column should be a flat list"
    # NDVI output shape should match raster data shape (num_bands=1, H, W)
    assert len(row['ndvi']) == 1 * height * width, f"Expected {height*width} elements in ndvi list, got {len(row['ndvi'])}"
    # Quick check on values (requires casting flattened list back to numpy)
    # This is optional and more involved - requires care with data types
    # try:
    #    ndvi_array = np.array(row['ndvi'], dtype=np.float32).reshape((1, height, width))
    #    assert np.nanmin(ndvi_array) >= -1.0 and np.nanmax(ndvi_array) <= 1.0, "NDVI values out of expected range [-1, 1]"
    # except Exception as e:
    #    print(f"Warning: Could not perform detailed NDVI value check: {e}")

    # Check metadata fields
    assert row['crs'] == ref_spec.cog_crs
    assert isinstance(row['bounds'], list) and len(row['bounds']) == 4
    # Compare timestamp (allow for slight precision differences if needed)
    assert isinstance(row['datetime'], datetime)
    expected_dt = datetime.fromisoformat(ref_spec.datetime_utc_iso.replace("Z", "+00:00")) if ref_spec.datetime_utc_iso else None
    assert row['datetime'] == expected_dt if expected_dt else row['datetime'] is None