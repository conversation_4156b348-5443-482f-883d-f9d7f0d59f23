# libs/tfw_engine_core/tests/runtime_ray/test_planner_unit.py

import pytest
import pyarrow as pa
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
import os
from datetime import datetime
import pytz
from dataclasses import fields # To help build mock data

from shapely.geometry import box
from rasterio.windows import Window

# Import the target module and types
from terrafloww.engine_core.runtime_ray import planner
from terrafloww.engine_core.runtime_ray.common_types import WindowSpec
from terrafloww.processing_engine.v1 import processing_engine_pb2

import logging

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s", stream=sys.stdout
)
logger = logging.getLogger(__name__)

# --- Constants for Tests ---
TEST_COLLECTION = "sentinel-2-l2a-mock"
TEST_BAND_RED = "red"
TEST_BAND_NIR = "nir"
TEST_SCENE_ID = "S2_TEST_SCENE_123"
TEST_CRS = "EPSG:32630"
TEST_TRANSFORM = (10.0, 0.0, 600000.0, 0.0, -10.0, 5000000.0) # Example 10m UTM
TEST_AOI_WKT = box(600050, 4999950, 600150, 5000050).wkt # Small box within the transform
TEST_DATETIME_STR = "2024-03-05T10:30:00Z"
TEST_DATETIME_OBJ = datetime.fromisoformat(TEST_DATETIME_STR.replace("Z", "+00:00"))

# (Continuing in test_planner_unit.py)

# --- Helper Functions ---

def create_test_workflow_plan(
    collection=TEST_COLLECTION,
    bands=[TEST_BAND_RED],
    aoi_wkt=TEST_AOI_WKT,
    aoi_crs="EPSG:4326", # Assume AOI is typically Lon/Lat initially
    datetime_filter="2024-03-01T00:00:00Z/2024-03-10T23:59:59Z",
    scene_limit=0,
    head_limit=0,
    apply_steps=[] # List of {"function_id": "name", "parameters": {}} dicts
) -> processing_engine_pb2.WorkflowPlan:
    """Creates a WorkflowPlan protobuf for testing."""
    plan = processing_engine_pb2.WorkflowPlan()
    plan.load_step.collection = collection
    plan.load_step.bands.extend(bands)
    plan.load_step.aoi_wkt = aoi_wkt
    plan.load_step.aoi_crs = aoi_crs
    plan.load_step.datetime_filter = datetime_filter
    plan.load_step.scene_limit = scene_limit
    plan.head_limit = head_limit

    for step_info in apply_steps:
        apply_func = plan.apply_steps.add()
        apply_func.function_id = step_info["function_id"]
        # Note: Protobuf Struct conversion is complex to mock simply,
        # The planner currently only reads function_id, so we can skip params here.
        # If planner needs params, mock Struct creation needed.

    return plan

def create_mock_assets_pylist(
    num_rows=1, scene_id=TEST_SCENE_ID, collection=TEST_COLLECTION, band="red",
    transform=TEST_TRANSFORM, crs=TEST_CRS, dt=TEST_DATETIME_OBJ, **overrides
) -> list:
    """Creates a list of dicts simulating asset rows from the catalog."""
    rows = []
    base_row = {
        "scene_id": scene_id,
        "collection": collection,
        "datetime": dt, # Use datetime object
        "year": dt.year,
        "month": dt.month,
        "geometry_wkt": None, # Not strictly needed by planner if AOI check is done
        "bbox_minx": transform[2], # Simplistic bbox based on transform origin
        "bbox_miny": transform[5] - 10980 * abs(transform[4]), # Assuming 10980 height
        "bbox_maxx": transform[2] + 10980 * abs(transform[0]), # Assuming 10980 width
        "bbox_maxy": transform[5],
        "cloud_cover": 10.0,
        "stac_properties_json": "{}",
        "stac_assets_json": "{}",
        "cog_key": band,
        "cog_href": f"s3://mock-bucket/{scene_id}/{band}.tif",
        "cog_title": f"{band} band",
        "cog_roles": ["data"],
        "cog_width": 10980, # Standard Sentinel-2 tile size
        "cog_height": 10980,
        "cog_tile_width": 1024,
        "cog_tile_height": 1024,
        "cog_transform": list(transform), # Store as list to match catalog output
        "cog_crs": crs,
        "cog_scale": 1.0, # Default or override
        "cog_offset": 0.0, # Default or override
        "cog_dtype": "uint16",
        "predictor": 1, # No prediction
        "cog_compression": "deflate", # Example
        # Simulate realistic tile offsets/counts for a 11x11 grid of 1024x1024 tiles
        "cog_tile_offsets": list(range(0, 121 * 1024 * 1024, 1024 * 1024)), # Dummy offsets
        "cog_tile_byte_counts": [1024 * 1024] * 121, # Dummy sizes (1MB per tile)
    }

    for i in range(num_rows):
        row = base_row.copy()
        # Allow overriding specific fields for different test cases
        row.update(overrides)
        # Ensure required fields for the loop exist
        if "cog_transform" not in row: row["cog_transform"] = list(transform)
        if "datetime" not in row: row["datetime"] = dt
        rows.append(row)

    return rows

def create_mock_assets_table(pylist: list) -> pa.Table:
    """Converts the pylist to a PyArrow Table with approximated schema."""
    if not pylist:
        # Create empty table with estimated schema if list is empty
        # Need to define schema more precisely based on EXT_STAC_IMG_DATASETS_SCHEMA
        # For now, create an empty table, schema inference might fail later
         return pa.table({}) # Placeholder

    # Infer schema from first row, then ensure types match planner expectations
    # This is simpler than replicating the full EXT_STAC schema exactly here
    table = pa.Table.from_pylist(pylist)

    # Ensure specific column types expected by planner
    schema_builder = {}
    for field in table.schema:
        if field.name == "datetime" and not pa.types.is_timestamp(field.type):
            schema_builder[field.name] = pa.timestamp('us', tz='UTC')
        elif field.name == "cog_transform" and field.type != pa.list_(pa.float64()):
             # Attempt cast if possible, maybe requires intermediate step
             schema_builder[field.name] = pa.list_(pa.float64())
        elif field.name == "cog_tile_offsets" and field.type != pa.list_(pa.int64()):
             schema_builder[field.name] = pa.list_(pa.int64())
        elif field.name == "cog_tile_byte_counts" and field.type != pa.list_(pa.int64()):
             schema_builder[field.name] = pa.list_(pa.int64())
        elif field.name == "predictor" and field.type != pa.int32():
             schema_builder[field.name] = pa.int32()
        # Add other necessary type corrections based on planner's usage
        else:
            schema_builder[field.name] = field.type # Keep original type

    target_schema = pa.schema(schema_builder)

    # Cast table if schema differs significantly
    try:
        if not table.schema.equals(target_schema, check_metadata=False):
             table = table.cast(target_schema)
    except Exception as e:
        print(f"Warning: Failed to cast mock table to target schema: {e}")
        # Proceed with potentially incorrect schema, test might fail

    return table


def create_mock_intersecting_tiles(num_tiles=1) -> list:
    """Returns a list of dicts simulating grid.get_intersecting_tiles_and_windows output."""
    tiles = []
    for i in range(num_tiles):
        # Simulate different tiles, e.g., tile (5, 5)
        tile_r = 5 + i
        tile_c = 5 + i
        # Simulate a window within that tile, e.g., offset 10,10, size 100x100
        window = Window(col_off=10, row_off=10, width=100, height=100)
        tiles.append({"tile_r": tile_r, "tile_c": tile_c, "window": window})
    return tiles


# (Continuing in test_planner_unit.py)
@pytest.mark.asyncio
async def test_plan_execution_basic_workflow(mocker):
    """Test the happy path: valid plan, matching assets, intersecting tiles."""
    # --- Mock Dependencies (same as before) ---
    mock_catalog = MagicMock()
    mock_assets_pylist = create_mock_assets_pylist(num_rows=1, band=TEST_BAND_RED)
    mock_assets_table = create_mock_assets_table(mock_assets_pylist)
    mock_catalog.query_assets = AsyncMock(return_value=mock_assets_table)
    mocker.patch('terrafloww.engine_core.runtime_ray.planner.CatalogClient', return_value=mock_catalog)

    mock_tiles = create_mock_intersecting_tiles(num_tiles=1)
    mocker.patch('terrafloww.engine_core.runtime_ray.planner.grid.get_intersecting_tiles_and_windows', return_value=mock_tiles)

    mock_reproject = mocker.patch('terrafloww.engine_core.runtime_ray.planner.engine_utils._reproject_geometry')
    mock_reproject.side_effect = lambda geom, src, dst: geom

    # --- Create Plan ---
    test_plan = create_test_workflow_plan(bands=[TEST_BAND_RED], aoi_wkt=TEST_AOI_WKT)

    # --- Run Planner and collect results ---
    window_specs_list = []
    async for spec in planner.plan_execution(test_plan): # Corrected: Use async for
         window_specs_list.append(spec)

    # --- Assertions ---
    assert isinstance(window_specs_list, list)
    expected_number_of_specs = 1 # Define the expected number based on mocks
    assert len(window_specs_list) == expected_number_of_specs
    # ... rest of assertions on window_specs_list[0] ...
    # (Keep the detailed assertions from before)
    spec = window_specs_list[0]
    assert isinstance(spec, WindowSpec)
    assert spec.band_name == TEST_BAND_RED
    # ... (all other specific assertions) ...
    mock_catalog.query_assets.assert_called_once()
    planner.grid.get_intersecting_tiles_and_windows.assert_called_once()
    planner.engine_utils._reproject_geometry.assert_called_once()


@pytest.mark.asyncio
async def test_plan_execution_no_assets_found(mocker):
    """Test when the catalog query returns no assets."""
    mock_catalog = MagicMock()
    mock_empty_table = create_mock_assets_table([])
    mock_catalog.query_assets = AsyncMock(return_value=mock_empty_table)
    mocker.patch('terrafloww.engine_core.runtime_ray.planner.CatalogClient', return_value=mock_catalog)
    mock_grid = mocker.patch('terrafloww.engine_core.runtime_ray.planner.grid.get_intersecting_tiles_and_windows')

    test_plan = create_test_workflow_plan()

    # --- Run Planner and collect results ---
    window_specs_list = []
    async for spec in planner.plan_execution(test_plan): # Corrected: Use async for
         window_specs_list.append(spec)

    # --- Assertions ---
    assert window_specs_list == [] # Expect empty list
    mock_catalog.query_assets.assert_called_once()
    mock_grid.assert_not_called()


@pytest.mark.asyncio
async def test_plan_execution_band_filter_mismatch(mocker):
    """Test when assets exist but none match the required bands."""
    mock_catalog = MagicMock()
    mock_assets_pylist = create_mock_assets_pylist(band=TEST_BAND_RED) + create_mock_assets_pylist(band=TEST_BAND_NIR)
    mock_assets_table = create_mock_assets_table(mock_assets_pylist)
    mock_catalog.query_assets = AsyncMock(return_value=mock_assets_table)
    mocker.patch('terrafloww.engine_core.runtime_ray.planner.CatalogClient', return_value=mock_catalog)
    mock_grid = mocker.patch('terrafloww.engine_core.runtime_ray.planner.grid.get_intersecting_tiles_and_windows')

    test_plan = create_test_workflow_plan(bands=["blue"]) # Request 'blue'

    # --- Run Planner and collect results ---
    window_specs_list = []
    async for spec in planner.plan_execution(test_plan): # Corrected: Use async for
         window_specs_list.append(spec)

    # --- Assertions ---
    assert window_specs_list == [] # Expect empty list
    mock_catalog.query_assets.assert_called_once()
    # mock_grid might or might not be called depending on filter timing


@pytest.mark.asyncio
async def test_plan_execution_ndvi_band_filtering(mocker):
    """Test that NDVI apply step correctly filters for red/nir bands."""
    mock_catalog = MagicMock()
    mock_assets_pylist = (
        create_mock_assets_pylist(band=TEST_BAND_RED) +
        create_mock_assets_pylist(band=TEST_BAND_NIR) +
        create_mock_assets_pylist(band="blue")
    )
    mock_assets_table = create_mock_assets_table(mock_assets_pylist)
    mock_catalog.query_assets = AsyncMock(return_value=mock_assets_table)
    mocker.patch('terrafloww.engine_core.runtime_ray.planner.CatalogClient', return_value=mock_catalog)

    mock_tiles = create_mock_intersecting_tiles(num_tiles=1)
    mocker.patch('terrafloww.engine_core.runtime_ray.planner.grid.get_intersecting_tiles_and_windows', return_value=mock_tiles)
    mocker.patch('terrafloww.engine_core.runtime_ray.planner.engine_utils._reproject_geometry').side_effect = lambda g, s, t: g

    test_plan = create_test_workflow_plan(
        bands=[],
        aoi_wkt=TEST_AOI_WKT,
        apply_steps=[{"function_id": "ndvi"}]
    )

    # --- Run Planner and collect results ---
    window_specs_list = []
    async for spec in planner.plan_execution(test_plan): # Corrected: Use async for
         window_specs_list.append(spec)

    # --- Assertions ---
    assert len(window_specs_list) == 2 # Expect 2 specs (red & nir)
    bands_in_specs = {spec.band_name for spec in window_specs_list}
    assert bands_in_specs == {TEST_BAND_RED, TEST_BAND_NIR}


@pytest.mark.asyncio
async def test_plan_execution_head_limit_is_ignored_by_planner(mocker):
    # NOTE: This test name is changed slightly. The planner *ignores* head_limit now.
    # The driver will apply it. This test verifies the planner yields everything.
    """Test that planner yields all specs regardless of head_limit (driver handles limit)."""
    mock_catalog = MagicMock()
    scene1_id = "SCENE1"
    scene2_id = "SCENE2"
    mock_assets_pylist = (
        create_mock_assets_pylist(scene_id=scene1_id, band=TEST_BAND_RED) +
        create_mock_assets_pylist(scene_id=scene1_id, band=TEST_BAND_NIR) +
        create_mock_assets_pylist(scene_id=scene2_id, band=TEST_BAND_RED) +
        create_mock_assets_pylist(scene_id=scene2_id, band=TEST_BAND_NIR)
    )
    mock_assets_table = create_mock_assets_table(mock_assets_pylist)
    mock_catalog.query_assets = AsyncMock(return_value=mock_assets_table)
    mocker.patch('terrafloww.engine_core.runtime_ray.planner.CatalogClient', return_value=mock_catalog)

    mock_tiles = create_mock_intersecting_tiles(num_tiles=2) # tile (5,5) and (6,6)
    mocker.patch('terrafloww.engine_core.runtime_ray.planner.grid.get_intersecting_tiles_and_windows', return_value=mock_tiles)
    mocker.patch('terrafloww.engine_core.runtime_ray.planner.engine_utils._reproject_geometry').side_effect = lambda g, s, t: g

    # Plan has head_limit=3, but planner should ignore it
    test_plan = create_test_workflow_plan(bands=[TEST_BAND_RED, TEST_BAND_NIR], aoi_wkt=TEST_AOI_WKT, head_limit=3)

    # --- Run Planner and collect results ---
    window_specs_list = []
    async for spec in planner.plan_execution(test_plan): # Corrected: Use async for
         window_specs_list.append(spec)

    # --- Assertions ---
    # Expect ALL specs: 2 scenes * 2 bands * 2 tiles = 8 specs
    assert len(window_specs_list) == 8


@pytest.mark.asyncio
async def test_plan_execution_skips_invalid_asset_data(mocker):
    """Test that assets with missing essential data are skipped."""
    mock_catalog = MagicMock()
    mock_assets_pylist = (
        create_mock_assets_pylist(band="good_band") +
        create_mock_assets_pylist(band="bad_band", cog_transform=None) # Missing transform
    )
    mock_assets_table = create_mock_assets_table(mock_assets_pylist)
    mock_catalog.query_assets = AsyncMock(return_value=mock_assets_table)
    mocker.patch('terrafloww.engine_core.runtime_ray.planner.CatalogClient', return_value=mock_catalog)

    mock_tiles = create_mock_intersecting_tiles(num_tiles=1)
    mocker.patch('terrafloww.engine_core.runtime_ray.planner.grid.get_intersecting_tiles_and_windows', return_value=mock_tiles)
    mocker.patch('terrafloww.engine_core.runtime_ray.planner.engine_utils._reproject_geometry').side_effect = lambda g, s, t: g

    test_plan = create_test_workflow_plan(bands=["good_band", "bad_band"], aoi_wkt=TEST_AOI_WKT)

    # --- Run Planner and collect results ---
    window_specs_list = []
    async for spec in planner.plan_execution(test_plan): # Corrected: Use async for
         window_specs_list.append(spec)

    # --- Assertions ---
    assert len(window_specs_list) == 1
    assert window_specs_list[0].band_name == "good_band"