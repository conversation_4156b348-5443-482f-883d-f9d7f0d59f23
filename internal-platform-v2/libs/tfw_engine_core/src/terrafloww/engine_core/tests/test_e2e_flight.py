# services/processing_engine/tests/test_e2e_flight.py

import pytest
import pyarrow as pa
import pyarrow.flight as flight
import numpy as np

from datetime import datetime
import pytz

import os
import uuid
import subprocess # To run main.py
import time
import signal # To terminate subprocess
import asyncio
import threading


# Import the placeholder caches and lock from the driver module
# Need to ensure driver is importable in test environment
try:
    from terrafloww.engine_core.runtime_ray.driver import (
        LOCAL_FLIGHT_RESULT_CACHE,
        LOCAL_FLIGHT_STATUS_CACHE,
        _FLIGHT_LOCK
    )

    from processing_engine.app.flight_server import FlightServer

    _TEST_SYNC_LOCK = FlightServer._sync_lock # Use the SAME lock object

except ImportError as e:
    print(f"Error importing FlightServer or driver caches: {e}")
    print("PYTHONPATH:", os.environ.get('PYTHONPATH'))
    print("Current working directory:", os.getcwd())
    print("Cannot import driver caches, or FlightServer lock skipping Flight E2E test.")
    pytest.skip("Cannot import driver caches, or FlightServer lock skipping Flight E2E test.", allow_module_level=True)

# Import the target schema for verification
from tfw_raster_schemas.raster import RASTER_CHUNK_SCHEMA


@pytest.fixture(scope="module")
def backend_server():
    """
    Starts the FlightServer (only) in a background thread for testing.
    Uses direct instantiation, bypassing services/processing_engine/app/main.py.
    Allows test to access shared cache/lock state directly.
    NOTE: This fixture does not test the combined gRPC+Flight startup in main.py.
    """
    # Define ports explicitly or get from env for consistency
    flight_port = int(os.environ.get("FLIGHT_PORT", "50052"))
    location = f"grpc+tcp://0.0.0.0:{flight_port}" # Use correct format

    server = FlightServer(location=location) # Pass location to constructor
    thread = threading.Thread(target=server.serve, daemon=True)
    thread.start()
    time.sleep(1) # Allow time for socket binding
    yield # Test runs here
    # Gracefully shut down
    try:
         print("\nShutting down test Flight server...")
         server.shutdown() # Use shutdown method
         thread.join(timeout=5)
         print("Test Flight server shut down.")
    except Exception as e:
         print(f"Error during test server shutdown: {e}")

@pytest.mark.asyncio
async def test_flight_retrieval_after_simulated_completion(backend_server):
    """
    Tests Flight do_get by simulating driver completion state in the cache.
    """
    # --- 1. Simulate Driver Completion ---
    execution_id = f"test_flight_{uuid.uuid4().hex[:6]}"
    ticket_str = f"ticket_for_{execution_id}"
    ticket = flight.Ticket(ticket_str.encode())

    # --- Create the mock table ---
    final_schema_list = list(RASTER_CHUNK_SCHEMA) + [pa.field("ndvi", pa.list_(pa.float32()))]
    final_schema = pa.schema(final_schema_list)
    # ... (dummy_data creation remains the same) ...
    C, H, W = 2, 5, 5; num_raster_elements=C*H*W; num_ndvi_elements=H*W
    dummy_data = {
        "chunk_id": [f"{execution_id}_chunk1"], "raster_data": [list(np.arange(num_raster_elements, dtype=np.float32))],
        "shape": [[C, H, W]], "bounds": [[1.0, 2.0, 3.0, 4.0]], "crs": ["EPSG:4326"],
        "datetime": [datetime.now(pytz.UTC)], "bands": [["nir", "red"]], "label": [None],
        "quality": [{"tile_r": 1.0, "tile_c": 1.0}], "ndvi": [list(np.arange(num_ndvi_elements, dtype=np.float32) * 0.1)]
    }
    try:
        mock_result_table = pa.Table.from_pydict(dummy_data, schema=final_schema)
        # --- Added Logging ---
        print(f"Mock Table Type: {type(mock_result_table)}")
        print(f"Mock Table Schema:\n{mock_result_table.schema}")
        print(f"Mock Table nbytes: {mock_result_table.nbytes}")
        # --- End Added Logging ---
    except Exception as e:
        pytest.fail(f"Failed to create mock Arrow table: {e}")


    print(f"Attempting to place table in cache for {execution_id} using sync lock...")
    with _TEST_SYNC_LOCK: # <--- Use the sync lock
        LOCAL_FLIGHT_STATUS_CACHE[execution_id] = {
            "status": "COMPLETED",
            "details": {"result_cached": True, "num_rows": mock_result_table.num_rows}
        }
        LOCAL_FLIGHT_RESULT_CACHE[execution_id] = mock_result_table
        print(f"Table placed in cache. Cache keys: {list(LOCAL_FLIGHT_RESULT_CACHE.keys())}")
    print(f"Simulated completion state for execution_id: {execution_id}")

    # Add a small sleep to ensure cache write is visible
    await asyncio.sleep(0.1) # 100ms

    # --- 2. Connect Flight Client ---
    flight_port = os.environ.get("FLIGHT_PORT", "50052")
    flight_uri = f"grpc+tcp://localhost:{flight_port}"
    try:
        client = flight.connect(flight_uri)
        # Verify connection (optional: list flights or get schema)
        # flights = list(client.list_flights()) # Might require list_flights implementation
        print(f"Flight client connected to {flight_uri}")
    except Exception as e:
        pytest.fail(f"Failed to connect Flight client to {flight_uri}: {e}")


    result_table = None
    # --- 3. Flight do_get ---
    try:
        print(f"Attempting do_get for ticket: {ticket_str}")
        reader = client.do_get(ticket)
        # Read the entire result into a table
        result_table = reader.read_all()
        print("Flight do_get successful, received table.")
    except Exception as e:
        # Clean up cache state if test fails here
        async with _FLIGHT_LOCK:
             if execution_id in LOCAL_FLIGHT_RESULT_CACHE: del LOCAL_FLIGHT_RESULT_CACHE[execution_id]
             if execution_id in LOCAL_FLIGHT_STATUS_CACHE: del LOCAL_FLIGHT_STATUS_CACHE[execution_id]
        pytest.fail(f"Flight do_get failed for ticket {ticket_str}: {e}")

    # --- 4. Assertions ---
    assert result_table is not None
    assert isinstance(result_table, pa.Table)
    assert result_table.num_rows == mock_result_table.num_rows
    # Check schema (allow for potential type differences if needed, e.g., float32 vs float64)
    assert result_table.schema.equals(mock_result_table.schema), f"Schema mismatch.\nExpected:\n{mock_result_table.schema}\nActual:\n{result_table.schema}"
    # Optional: Compare content (might be slow for large data)
    # assert result_table.equals(mock_result_table)

    # --- Cleanup (using sync lock) ---
    print(f"Cleaning up cache for {execution_id} using sync lock...")
    with _TEST_SYNC_LOCK:
         if execution_id in LOCAL_FLIGHT_RESULT_CACHE: del LOCAL_FLIGHT_RESULT_CACHE[execution_id]
         if execution_id in LOCAL_FLIGHT_STATUS_CACHE: del LOCAL_FLIGHT_STATUS_CACHE[execution_id]
    print("Cache cleanup done.")