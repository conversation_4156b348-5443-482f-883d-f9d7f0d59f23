# Repository Structure

## Main Components

### SDK Layer
```markdown
sdk/
└── src/
    └── terrafloww/
        └── sdk/
            └── tesseract/
                ├── collections.py      ✔  # builds the pipeline, `.compute()`
                ├── loaders.py          ✔  # defines lazy Sentinel-2 loader
                ├── exceptions.py       –  # custom errors not raised in NDVI test
                └── __init__.py         –
```

### Test Suite
```markdown
sdk/
└── src/
    └── tests/
        └── test_ndvi.py              ✔  # NDVI smoke test
```

### Services
```markdown
services/
└── processing_engine/
    └── app/
        ├── grpc_service.py           ✔  # receives ExecuteWorkflow, kicks off RayDriver
        ├── flight_server.py          ✔  # streams results back via Arrow-Flight
        └── __init__.py               –
```

### Core Engine
```markdown
libs/
└── tfw_engine_core/
    └── src/
        └── terrafloww/
            └── engine_core/
                ├── runtime_ray/
                │   ├── driver.py      ✔  # orchestrates Ray tasks + writes Flight cache
                │   ├── planner.py     ✔  # turns high-level plan into WindowSpecs
                │   └── worker.py      ✔  # fetches COGs, runs kernels, returns RecordBatches
                │
                ├── process.py        ✔  # contains the `_ndvi_kernel` implementation
                ├── catalog_client.py ✔  # STAC catalog lookup + band-alias resolution
                ├── registry.py       –  # generic function registry
                ├── utils.py          –  # reprojection, Arrow/NumPy helpers
                └── spectral/
                    └── ndvi.py       ✔  # NDVI wrapper (if split out)
                └── bands/
                    └── sentinel2.py  –  # per-collection mapping
```

## NDVI Flow Components

### SDK Layer
- `collections.py`: Builds the DAG, calls gRPC & Flight
- `loaders.py`: Lazy-loads Sentinel-2 COG URLs

### Test
- `test_ndvi.py`: NDVI smoke test

### gRPC API
- `grpc_service.py`: Accepts ExecuteWorkflow

### Ray Driver Stack
- `driver.py`: Drives Ray jobs, collects + caches results
- `planner.py`: Slices AOI into tiles (WindowSpecs)
- `worker.py`: Fetches imagery & applies _ndvi_kernel

### Processing
- `process.py`: Core NDVI logic
- `ndvi.py`: Kernel entry-point (if separated)

### Catalog
- `catalog_client.py`: Resolves band aliases, queries STAC

### Flight Server
- `flight_server.py`: Serves Arrow table on do_get

## Supporting Files

### SDK
- `exceptions.py`: Custom error handling

### Flight Server
- Additional endpoints: `do_put`, `list_flights`, `get_flight_info`

### Core Engine Support
- `registry.py`: Function registry
- `utils.py`: General reprojection and Arrow helpers
- `bands/sentinel2.py`: Collection mapping

### Documentation & Infrastructure
- `README.md`
- CI configurations
- Dockerfiles
- Examples