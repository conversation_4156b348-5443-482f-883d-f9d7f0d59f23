# Linear Project Breakdown: Geospatial Data Platform

Based on Linear's philosophy of shipping fast and the actual tasks in `new_tasks.md`, here's a focused breakdown to get the platform working end-to-end with K8s demo capability.

## 🎯 Projects Overview

### Project 1: SDK Feature Complete
**Goal:** Complete SDK with filter(), iter_batches() and all tests passing
**Timeline:** 2-3 days
**Owner:** SDK Team

### Project 2: Platform Reliability & Integration
**Goal:** Fix Flight server cache issues and comprehensive testing
**Timeline:** 1-2 days  
**Owner:** Platform Team

### Project 3: Kubernetes Demo Deployment
**Goal:** Working K8s deployment with KubeRay for demos
**Timeline:** 3-4 days
**Owner:** Platform Team

### Project 4: Developer Experience
**Goal:** Great documentation and easy setup process
**Timeline:** 1-2 days
**Owner:** DevRel Team

---

## 📅 Milestones

### Milestone 1: SDK Complete (Day 3)
- filter() and iter_batches() implemented
- All SDK tests passing
- End-to-end integration working

### Milestone 2: K8s Demo Ready (Day 7)
- Ray cluster running on K8s with KubeRay
- Processing engine deployed and working
- Basic Helm chart for easy deployment

### Milestone 3: Production Ready (Day 10)
- Flight server cache management solid
- Comprehensive documentation
- Easy developer onboarding

---

## 🔥 Issues by Project

### Project 1: SDK Feature Complete

#### **PLT-101** | P0 | 1d | Implement `filter()` method in GeoImageCollection
- **Labels:** `sdk`, `core-feature`, `arrow-native`
- **Description:** Add spatial and temporal filtering capabilities (from new_tasks.md 4.4)
- **Acceptance Criteria:**
  - Support AOI filtering (WKT, GeoJSON, Shapely geometries)
  - Support date range filtering (ISO 8601 format)
  - Support cloud cover and other metadata filters
  - Update workflow plan builder to add filter steps to protobuf WorkflowPlan
  - Integrate with existing planner logic
- **Technical Notes:** Implement in `collections.py`, update plan builder

#### **PLT-102** | P0 | 6h | Implement `iter_batches()` streaming method
- **Labels:** `sdk`, `streaming`, `performance`
- **Description:** Stream results lazily rather than full materialization (from new_tasks.md 4.5)
- **Acceptance Criteria:**
  - Async generator in `collections.py`
  - Use Flight reader APIs to yield each `RecordBatch`
  - Support user cancellation / early stop
  - Unit tests mocking multi-batch streams
- **Technical Notes:** Modify `_iter_batches_async()` method

#### **PLT-103** | P1 | 4h | Fix failing filter tests
- **Labels:** `testing`, `bugfix`
- **Description:** Make `test_geoimagecollection_operations.py` pass (from new_tasks.md 5.3)
- **Dependencies:** PLT-101
- **Acceptance Criteria:**
  - Fix `AttributeError: 'GeoImageCollection' object has no attribute 'filter'`
  - Update test to work with implemented filter functionality
  - Ensure all SDK tests pass

---

### Project 2: Platform Reliability & Integration

#### **PLT-201** | P0 | 8h | Implement Flight server stale-cache eviction/TTL
- **Labels:** `flight-server`, `caching`, `reliability`
- **Description:** Complete remaining subtask from new_tasks.md 3.2 - stale-cache eviction
- **Acceptance Criteria:**
  - Expire old cache entries after configurable TTL
  - Background cleanup process for memory management
  - Optional manual cleanup API
  - Prevent memory leaks in long-running servers
- **Technical Notes:** Update `flight_server.py` with TTL mechanism

#### **PLT-202** | P1 | 6h | End-to-End Flight/SDK Integration Test  
- **Labels:** `testing`, `integration`, `flight`
- **Description:** Validate Flight server and SDK client interop (from new_tasks.md 5.4)
- **Acceptance Criteria:**
  - Native `pyarrow.flight.FlightClient.get_flight_info` + `do_get` → table
  - Assert matching schema/row count
  - Simulate partial failures & retries
  - Test beyond SDK helper methods
- **Technical Notes:** Extend integration test suite

---

### Project 3: Kubernetes Demo Deployment  

#### **PLT-301** | P0 | 1d | Install KubeRay Operator and basic Ray cluster
- **Labels:** `kubernetes`, `kuberay`, `ray-cluster`
- **Description:** Deploy Ray cluster on Kubernetes using KubeRay operator (from new_tasks.md 6.1)
- **Acceptance Criteria:**
  - KubeRay operator installed with basic RBAC
  - Ray cluster custom resource manifests
  - Head node with dashboard accessible
  - Worker nodes with basic auto-scaling
  - Basic resource limits configured
- **Technical Notes:** Focus on working demo, not production hardening

#### **PLT-302** | P0 | 1d | Create custom Ray Docker image with platform dependencies
- **Labels:** `docker`, `ray`, `dependencies`
- **Description:** Ray image preparation for platform (from new_tasks.md 6.1)
- **Acceptance Criteria:**
  - Custom Ray Docker image with platform dependencies
  - Include required Python packages (pyarrow, rasterio, etc.)
  - Pre-install platform libraries (tfw-engine-core, etc.)
  - Image works with KubeRay deployment
- **Technical Notes:** Basic image, skip security scanning and optimization

#### **PLT-303** | P0 | 1d | Deploy processing engine as Kubernetes service
- **Labels:** `kubernetes`, `processing-engine`, `grpc`, `flight`
- **Description:** K8s deployment for processing engine (from new_tasks.md 6.2)
- **Acceptance Criteria:**
  - Basic Dockerfile improvements for K8s
  - Deployment manifest with basic resource limits  
  - Service manifest for gRPC (50051) and Flight (50052) ports
  - Basic health checks
  - Environment variables for RAY_ADDRESS, FLIGHT_HOST, FLIGHT_PORT
- **Technical Notes:** Skip HPA, anti-affinity, and advanced configs

#### **PLT-304** | P1 | 8h | Create basic Helm chart for deployment
- **Labels:** `helm`, `deployment`, `kubernetes`
- **Description:** Helm chart development for easy deployment (from new_tasks.md 6.3)
- **Acceptance Criteria:**
  - Basic chart directory structure
  - Chart.yaml with KubeRay dependency
  - values.yaml with configurable parameters
  - Templates for Ray cluster and processing engine
  - Simple deployment without environment-specific complexity
- **Technical Notes:** Focus on working deployment, skip advanced configuration management

---

### Project 4: Developer Experience

#### **PLT-401** | P1 | 6h | Improve README and setup documentation
- **Labels:** `documentation`, `onboarding`
- **Description:** Step-by-step developer setup guide (from new_tasks.md 6.1)
- **Acceptance Criteria:**
  - Step-by-step developer setup (STAC ingestion, env vars, Ray init)
  - CLI commands to ingest data, launch services & run tests
  - Quickstarts: `.filter()`, `.apply()`, `.compute()`, `.iter_batches()`
  - K8s deployment instructions
- **Technical Notes:** Update main README.md with clear instructions

#### **PLT-402** | P2 | 4h | Developer onboarding guide
- **Labels:** `documentation`, `developer-experience`
- **Description:** One-pager for developers (from new_tasks.md 6.2)
- **Acceptance Criteria:**
  - Repo structure and module responsibilities
  - Adding a new kernel
  - Running & debugging Flight server
  - Extending SDK
  - K8s deployment workflow
- **Technical Notes:** Create separate developer guide document

#### **PLT-403** | P2 | 4h | Basic GitHub Actions setup
- **Labels:** `ci-cd`, `testing`, `automation`
- **Description:** Basic monitoring & CI/CD (from new_tasks.md 6.3)
- **Acceptance Criteria:**
  - Linting, static types, unit tests
  - Basic Docker integration for testing
  - Integration test automation
  - Skip complex versioning and deployment pipelines
- **Technical Notes:** Simple GitHub Actions workflows

---

## 🚀 Sprint Planning Recommendations

### Sprint 1 (Days 1-3): Core SDK
- PLT-101: Implement filter() method
- PLT-102: Implement iter_batches()
- PLT-103: Fix failing tests
- PLT-201: Flight server cache management

### Sprint 2 (Days 4-7): Kubernetes Demo
- PLT-301: KubeRay operator and cluster
- PLT-302: Custom Ray Docker image  
- PLT-303: Processing engine K8s deployment
- PLT-202: End-to-end integration tests

### Sprint 3 (Days 8-10): Polish & Documentation
- PLT-304: Basic Helm chart
- PLT-401: README and setup docs
- PLT-402: Developer onboarding guide
- PLT-403: Basic CI/CD

---

## 📊 Success Metrics

- **SDK Complete:** filter() and iter_batches() working, all tests pass
- **K8s Demo:** Full workflow running on Kubernetes cluster
- **Developer Ready:** New developer can deploy in <30 minutes
- **Platform Stable:** <5% failure rate on integration tests

## 🔍 What's Excluded (Based on Requirements)

Intentionally excluded from new_tasks.md K8s section to focus on working demo:
- Service mesh (Istio/Linkerd) 
- Distributed tracing (Jaeger/Zipkin)
- Advanced monitoring (Prometheus/Grafana stacks)
- Cache layers (Redis/Memcached)
- GitOps workflows (ArgoCD/Flux)
- Advanced CI/CD versioning strategies
- Security hardening and compliance
- Advanced storage and persistence
- Production operations and runbooks

These can be added later after the working demo is validated.