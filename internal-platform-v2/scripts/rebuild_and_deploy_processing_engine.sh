#!/bin/bash
# SPDX-FileCopyrightText: Terrafloww Labs, 2025

# Script to rebuild and redeploy the processing engine with S3 support

set -e

# Configuration
NAMESPACE="terrafloww-platform"
DEPLOYMENT_NAME="terrafloww-processing-engine"
IMAGE_NAME="registry.digitalocean.com/terrafloww-dev/processing-engine"
IMAGE_TAG="latest"
FULL_IMAGE_NAME="${IMAGE_NAME}:${IMAGE_TAG}"

echo "🚀 Rebuilding and redeploying processing engine with S3 support..."

# Change to the project root
cd "$(dirname "$0")/.."

echo "📦 Building new Docker image with S3 dependencies..."

# Build the Docker image
docker build \
    -f services/processing_engine/Dockerfile \
    -t "$FULL_IMAGE_NAME" \
    .

echo "✅ Docker image built successfully: $FULL_IMAGE_NAME"

# Push to registry
echo "📤 Pushing image to DigitalOcean Container Registry..."
docker push "$FULL_IMAGE_NAME"

echo "✅ Image pushed successfully"

# Check if deployment exists
if ! kubectl get deployment "$DEPLOYMENT_NAME" -n "$NAMESPACE" >/dev/null 2>&1; then
    echo "📋 Deployment doesn't exist, applying deployment configuration..."
    kubectl apply -f infra/k8s/processing-engine-deployment.yaml
    kubectl apply -f infra/k8s/processing-engine-service.yaml
else
    echo "🔄 Restarting existing deployment to pick up new image..."
    kubectl rollout restart deployment/"$DEPLOYMENT_NAME" -n "$NAMESPACE"
fi

echo "⏳ Waiting for deployment to be ready..."
kubectl rollout status deployment/"$DEPLOYMENT_NAME" -n "$NAMESPACE" --timeout=300s

echo "🔍 Checking deployment status..."
kubectl get pods -n "$NAMESPACE" -l app=terrafloww,component=processing-engine

echo "📋 Checking environment variables in the pod..."
POD_NAME=$(kubectl get pods -n "$NAMESPACE" -l app=terrafloww,component=processing-engine -o jsonpath='{.items[0].metadata.name}')

if [[ -n "$POD_NAME" ]]; then
    echo "Pod: $POD_NAME"
    echo "Environment variables related to catalog:"
    kubectl exec -n "$NAMESPACE" "$POD_NAME" -- env | grep -E "(STAC_CATALOG|DO_)" || echo "No catalog environment variables found"
else
    echo "⚠️ No pods found for the processing engine"
fi

echo "🎉 Processing engine rebuild and deployment completed!"
echo ""
echo "Next steps:"
echo "1. Run catalog connectivity tests"
echo "2. Verify S3 catalog access is working"
echo "3. Test end-to-end workflow with cloud catalog"
