# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Test script for S3-based STAC ingestion.
This script tests the updated ingest_ext_stac.py with DigitalOcean Spaces.
"""

import os
import sys
import logging
import subprocess
from pathlib import Path
from dotenv import load_dotenv

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_credentials():
    """Load DigitalOcean credentials from environment file."""
    credentials_file = "/home/<USER>/Work/platform-build/do_key.env"
    
    if not os.path.exists(credentials_file):
        logger.error(f"Credentials file not found: {credentials_file}")
        return False
    
    # Load environment variables
    load_dotenv(credentials_file)
    
    # Check required variables
    required_vars = [
        "do_access_key_id",
        "do_secret_access_key", 
        "do_space_endpoint",
        "do_region"
    ]
    
    missing = []
    for var in required_vars:
        if not os.getenv(var):
            missing.append(var)
    
    if missing:
        logger.error(f"Missing required environment variables: {missing}")
        return False
    
    # Set up S3 catalog environment variables
    os.environ["STAC_CATALOG_S3_BUCKET"] = "dev-datasets"
    os.environ["STAC_CATALOG_S3_ENDPOINT"] = os.getenv("do_space_endpoint")
    os.environ["STAC_CATALOG_S3_REGION"] = os.getenv("do_region")
    os.environ["STAC_CATALOG_S3_PATH_PREFIX"] = "catalog"
    os.environ["DO_ACCESS_KEY_ID"] = os.getenv("do_access_key_id")
    os.environ["DO_SECRET_ACCESS_KEY"] = os.getenv("do_secret_access_key")
    os.environ["DO_REGION"] = os.getenv("do_region")
    os.environ["DO_SPACE_ENDPOINT"] = os.getenv("do_space_endpoint")
    
    logger.info("✅ Credentials loaded and S3 environment configured")
    return True

def test_s3_connectivity():
    """Test basic S3 connectivity before ingestion."""
    logger.info("🌐 Testing S3 connectivity...")
    
    try:
        import boto3
        from botocore.exceptions import ClientError
        
        # Create S3 client
        s3_client = boto3.client(
            's3',
            aws_access_key_id=os.environ["DO_ACCESS_KEY_ID"],
            aws_secret_access_key=os.environ["DO_SECRET_ACCESS_KEY"],
            endpoint_url=os.environ["DO_SPACE_ENDPOINT"],
            region_name=os.environ["DO_REGION"]
        )
        
        # Test bucket access
        bucket_name = os.environ["STAC_CATALOG_S3_BUCKET"]
        s3_client.head_bucket(Bucket=bucket_name)
        logger.info(f"✅ Successfully connected to S3 bucket: {bucket_name}")
        
        return True
        
    except ClientError as e:
        error_code = e.response['Error']['Code']
        if error_code == '404':
            logger.error(f"❌ Bucket {bucket_name} does not exist")
        elif error_code == '403':
            logger.error(f"❌ Access denied to bucket {bucket_name}")
        else:
            logger.error(f"❌ S3 error: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ Failed to connect to S3: {e}")
        return False

def run_stac_ingestion():
    """Run STAC ingestion with small test dataset."""
    logger.info("🚀 Starting STAC ingestion test...")
    
    # Configuration for small test
    stac_api = "https://earth-search.aws.element84.com/v1"
    collection = "sentinel-2-l2a"
    bbox = "-122.5,37.7,-122.3,37.9"  # Small area around San Francisco (more likely to have data)
    datetime_filter = "2024-06-01/2024-06-15"  # Recent 2-week period
    max_items = 3  # Very small test
    
    # Build command - use shell=True to handle negative coordinates properly
    script_path = Path(__file__).parent / "ingest_ext_stac.py"
    cmd_str = f'{sys.executable} "{script_path}" {stac_api} {collection} --bbox="{bbox}" --datetime="{datetime_filter}" --max-items={max_items} --batch-size=2 --limit=10 --log-level=INFO'
    
    logger.info(f"Running command: {cmd_str}")

    try:
        # Run the ingestion script
        result = subprocess.run(
            cmd_str,
            shell=True,
            capture_output=True,
            text=True,
            timeout=300,  # 5 minute timeout
            env=os.environ.copy()  # Pass current environment variables
        )
        
        if result.returncode == 0:
            logger.info("✅ STAC ingestion completed successfully!")
            logger.info("Output:")
            for line in result.stdout.split('\n'):
                if line.strip():
                    logger.info(f"  {line}")
        else:
            logger.error("❌ STAC ingestion failed!")
            logger.error("STDOUT:")
            for line in result.stdout.split('\n'):
                if line.strip():
                    logger.error(f"  {line}")
            logger.error("STDERR:")
            for line in result.stderr.split('\n'):
                if line.strip():
                    logger.error(f"  {line}")
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        logger.error("❌ STAC ingestion timed out after 5 minutes")
        return False
    except Exception as e:
        logger.error(f"❌ Error running STAC ingestion: {e}")
        return False

def verify_delta_table():
    """Verify that the Delta table was created in S3."""
    logger.info("🔍 Verifying Delta table creation...")
    
    try:
        import boto3
        
        s3_client = boto3.client(
            's3',
            aws_access_key_id=os.environ["DO_ACCESS_KEY_ID"],
            aws_secret_access_key=os.environ["DO_SECRET_ACCESS_KEY"],
            endpoint_url=os.environ["DO_SPACE_ENDPOINT"],
            region_name=os.environ["DO_REGION"]
        )
        
        bucket = os.environ["STAC_CATALOG_S3_BUCKET"]
        prefix = f"{os.environ['STAC_CATALOG_S3_PATH_PREFIX']}/ext_stac_datasets/"
        
        # List objects in the Delta table path
        response = s3_client.list_objects_v2(
            Bucket=bucket,
            Prefix=prefix,
            MaxKeys=10
        )
        
        if 'Contents' in response:
            logger.info(f"✅ Found {len(response['Contents'])} objects in Delta table:")
            for obj in response['Contents'][:5]:  # Show first 5
                logger.info(f"  - {obj['Key']} ({obj['Size']} bytes)")
            return True
        else:
            logger.warning("⚠️ No objects found in Delta table path")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error verifying Delta table: {e}")
        return False

def main():
    """Run the complete S3 STAC ingestion test."""
    logger.info("🧪 Starting S3 STAC ingestion test...")
    
    # Step 1: Load credentials
    if not load_credentials():
        logger.error("❌ Failed to load credentials")
        return False
    
    # Step 2: Test S3 connectivity
    if not test_s3_connectivity():
        logger.error("❌ S3 connectivity test failed")
        return False
    
    # Step 3: Run STAC ingestion
    if not run_stac_ingestion():
        logger.error("❌ STAC ingestion failed")
        return False
    
    # Step 4: Verify Delta table
    if not verify_delta_table():
        logger.error("❌ Delta table verification failed")
        return False
    
    logger.info("🎉 All tests passed! S3 STAC ingestion is working correctly.")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
