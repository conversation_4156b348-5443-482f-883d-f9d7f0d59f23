#!/bin/bash
# Script to clean up obsolete code after schema and utility reorganization

echo "Cleaning up obsolete code..."

# Create backup directory
BACKUP_DIR="/home/<USER>/Work/platform-build/internal-platform-v2/backup_obsolete_code_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR/libs/tfw_apis/src/terrafloww/api/models"

# Backup files before removing
echo "Creating backups in $BACKUP_DIR..."
cp /home/<USER>/Work/platform-build/internal-platform-v2/libs/tfw_apis/src/terrafloww/api/models/datasets.py "$BACKUP_DIR/libs/tfw_apis/src/terrafloww/api/models/"
cp /home/<USER>/Work/platform-build/internal-platform-v2/libs/tfw_apis/src/terrafloww/api/models/grid_templates.py "$BACKUP_DIR/libs/tfw_apis/src/terrafloww/api/models/"
cp /home/<USER>/Work/platform-build/internal-platform-v2/libs/tfw_apis/src/terrafloww/api/models/delta_utils.py "$BACKUP_DIR/libs/tfw_apis/src/terrafloww/api/models/"
cp /home/<USER>/Work/platform-build/internal-platform-v2/libs/tfw_apis/src/terrafloww/api/models/__init__.py "$BACKUP_DIR/libs/tfw_apis/src/terrafloww/api/models/"

# Create replacement files with deprecation notices
echo "Creating replacement files with deprecation notices..."

# Replace datasets.py
cat > /home/<USER>/Work/platform-build/internal-platform-v2/libs/tfw_apis/src/terrafloww/api/models/datasets.py << 'EOF'
"""
DEPRECATED: This module has been moved to tfw_raster_schemas.datasets

Please update your imports to use:
from tfw_raster_schemas import IMG_DATASETS_SCHEMA
"""

import warnings

warnings.warn(
    "The terrafloww.api.models.datasets module is deprecated. "
    "Please use tfw_raster_schemas.datasets instead.",
    DeprecationWarning,
    stacklevel=2
)

# Re-export from the new location for backward compatibility
from tfw_raster_schemas.datasets import IMG_DATASETS_SCHEMA
EOF

# Replace grid_templates.py
cat > /home/<USER>/Work/platform-build/internal-platform-v2/libs/tfw_apis/src/terrafloww/api/models/grid_templates.py << 'EOF'
"""
DEPRECATED: This module has been moved to tfw_raster_schemas.grid

Please update your imports to use:
from tfw_raster_schemas import GRID_TEMPLATES_SCHEMA
"""

import warnings

warnings.warn(
    "The terrafloww.api.models.grid_templates module is deprecated. "
    "Please use tfw_raster_schemas.grid instead.",
    DeprecationWarning,
    stacklevel=2
)

# Re-export from the new location for backward compatibility
from tfw_raster_schemas.grid import GRID_TEMPLATES_SCHEMA
EOF

# Replace delta_utils.py
cat > /home/<USER>/Work/platform-build/internal-platform-v2/libs/tfw_apis/src/terrafloww/api/models/delta_utils.py << 'EOF'
"""
DEPRECATED: This module has been reorganized.

Please update your imports to use:
- from tfw_core_utils import create_canonical_hash
- from services.metadata_service.app.crud.serialization import serialize_for_storage, deserialize_from_storage
"""

import warnings

warnings.warn(
    "The terrafloww.api.models.delta_utils module is deprecated. "
    "Please use tfw_core_utils for create_canonical_hash and "
    "services.metadata_service.app.crud.serialization for serialization utilities.",
    DeprecationWarning,
    stacklevel=2
)

# Re-export from the new locations for backward compatibility
from tfw_core_utils import create_canonical_hash

# Re-implement serialization functions to avoid circular imports
import json
from typing import Any, Optional

def serialize_for_storage(obj: Any) -> Optional[str]:
    """
    DEPRECATED: Use services.metadata_service.app.crud.serialization.serialize_for_storage
    """
    if obj is None or (isinstance(obj, (dict, list)) and len(obj) == 0):
        return None
    return json.dumps(obj, sort_keys=True)

def deserialize_from_storage(json_str: Optional[str]) -> Any:
    """
    DEPRECATED: Use services.metadata_service.app.crud.serialization.deserialize_from_storage
    """
    if not json_str:
        return None
    return json.loads(json_str)
EOF

# Update __init__.py
cat > /home/<USER>/Work/platform-build/internal-platform-v2/libs/tfw_apis/src/terrafloww/api/models/__init__.py << 'EOF'
"""
DEPRECATED: This module has been reorganized.

Please update your imports to use:
- from tfw_raster_schemas import IMG_DATASETS_SCHEMA, GRID_TEMPLATES_SCHEMA
- from tfw_core_utils import create_canonical_hash
"""

# Re-export from the new locations for backward compatibility
from tfw_raster_schemas import IMG_DATASETS_SCHEMA, GRID_TEMPLATES_SCHEMA
from tfw_core_utils import create_canonical_hash
from .delta_utils import serialize_for_storage, deserialize_from_storage
EOF

echo "Cleanup complete. Obsolete code has been replaced with deprecation notices."
echo "Backups of the original files are available in: $BACKUP_DIR"
