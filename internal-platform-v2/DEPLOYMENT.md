# Terrafloww Platform Deployment Guide

**Last Updated**: June 24, 2025  
**Status**: Production Ready ✅

## Overview

This guide covers the complete deployment process for the Terrafloww Platform, including recent architectural improvements for Ray worker networking and Flight server refactoring.

## Quick Start

```bash
# Deploy entire platform
./scripts/deploy-platform.sh all

# Test deployment
cd sdk && python src/tests/test_ndvi.py
```

## Architecture Overview

The Terrafloww Platform consists of:

1. **Processing Engine**: gRPC + Apache Arrow Flight server for workflow execution
2. **Ray Cluster**: Distributed computing cluster for parallel processing
3. **Data Ingestion**: STAC catalog ingestion and management
4. **SDK**: Python client library for workflow execution

### Recent Architectural Improvements

#### 1. Flight Server Refactor (June 2025)
- **Problem**: Race conditions and data loss in Ray worker results
- **Solution**: Stateful Flight Server with direct worker uploads
- **Result**: 100% data integrity, eliminated race conditions

#### 2. Ray Worker Networking Fix (June 2025)  
- **Problem**: Connection drops due to external LoadBalancer routing
- **Solution**: Internal Kubernetes service DNS for pod-to-pod communication
- **Result**: Reliable connections, improved performance

## Deployment Scripts

### 1. Unified Platform Deployment
```bash
./scripts/deploy-platform.sh [component]
```
- **Components**: `ray`, `processing-engine`, `all`
- **Features**: Git SHA + timestamp tagging, immutable deployments
- **Best Practice**: Always use for production deployments

### 2. Individual Component Scripts
```bash
# Ray cluster only
./infra/k8s/build-ray-image.sh

# Processing engine only  
./scripts/rebuild_and_deploy_processing_engine.sh
```

### 3. Image Tagging Strategy
All images use immutable tags: `{git-sha}-{timestamp}`
- Example: `a1b2c3d-********-153045`
- Benefits: Traceability, rollback capability, no cache issues

## Prerequisites

### 1. Kubernetes Cluster
- DigitalOcean Kubernetes cluster
- Namespace: `terrafloww-platform`
- KubeRay operator installed

### 2. Container Registry
- DigitalOcean Container Registry: `registry.digitalocean.com/terrafloww-dev`
- Authentication configured via `doctl`

### 3. Required Secrets
```bash
# Apply catalog secrets
kubectl apply -f infra/k8s/catalog-secrets.yaml
```

### 4. Local Tools
- Docker
- kubectl
- doctl (DigitalOcean CLI)
- Git

## Deployment Procedures

### Full Platform Deployment

1. **Deploy Ray Cluster**:
   ```bash
   ./scripts/deploy-platform.sh ray
   ```
   - Builds custom Ray image with internal libraries
   - Updates ray-cluster.yaml with new image tag
   - Deploys Ray cluster to Kubernetes
   - Waits for pods to be ready

2. **Deploy Processing Engine**:
   ```bash
   ./scripts/deploy-platform.sh processing-engine
   ```
   - Builds processing engine image
   - Updates deployment.yaml with new image tag
   - Applies Kubernetes manifests
   - Waits for rollout completion

3. **Deploy Everything**:
   ```bash
   ./scripts/deploy-platform.sh all
   ```

### Code-to-Deployment Mapping

| Code Changes | Requires Rebuild | Deployment Script |
|--------------|------------------|-------------------|
| Ray worker code (`libs/tfw_engine_core/runtime_ray/worker.py`) | Ray cluster | `deploy-platform.sh ray` |
| Flight server (`services/processing_engine/app/flight_server.py`) | Processing engine | `deploy-platform.sh processing-engine` |
| Driver code (`libs/tfw_engine_core/runtime_ray/driver.py`) | Both | `deploy-platform.sh all` |
| Internal libraries (`libs/tfw_*`) | Both | `deploy-platform.sh all` |
| Kubernetes configs (`infra/k8s/*.yaml`) | Apply configs | `kubectl apply -f` |

## Testing and Validation

### 1. End-to-End Test
```bash
cd sdk
python src/tests/test_ndvi.py
```

**Expected Output**:
```
Result table shape: 2 rows, 10 columns
result num rows is as expected ✅
ndvi present in result ✅
unique windows are as expected ✅
```

### 2. Component Health Checks
```bash
# Check all pods
kubectl get pods -n terrafloww-platform

# Check Ray cluster
kubectl get raycluster -n terrafloww-platform

# Check processing engine logs
kubectl logs -n terrafloww-platform -l app=terrafloww,component=processing-engine
```

### 3. Connectivity Tests
```bash
# Test Ray cluster connectivity
kubectl exec -n terrafloww-platform -it terrafloww-ray-cluster-head-xxx -- ray status

# Test Flight server connectivity
kubectl port-forward -n terrafloww-platform svc/terrafloww-processing-engine-svc 50052:50052
```

## Troubleshooting

### Common Issues

#### 1. Ray Workers Can't Connect to Flight Server
**Symptoms**: Connection timeouts, "do_put" errors
**Solution**: Verify internal DNS configuration
```bash
kubectl exec -n terrafloww-platform terrafloww-processing-engine-xxx -- env | grep FLIGHT_INTERNAL
```

#### 2. Image Pull Errors
**Symptoms**: `ImagePullBackOff` status
**Solution**: Check registry authentication
```bash
doctl registry login
kubectl get secret registry-terrafloww-dev -n terrafloww-platform
```

#### 3. Ray Cluster Not Ready
**Symptoms**: Ray pods stuck in `Pending` or `CrashLoopBackOff`
**Solution**: Check resource limits and node capacity
```bash
kubectl describe pod -n terrafloww-platform -l ray.io/cluster=terrafloww-ray-cluster
```

### Log Analysis
```bash
# Processing engine logs
kubectl logs -n terrafloww-platform -l app=terrafloww,component=processing-engine --tail=100

# Ray head logs  
kubectl logs -n terrafloww-platform -l ray.io/node-type=head --tail=100

# Ray worker logs
kubectl logs -n terrafloww-platform -l ray.io/node-type=worker --tail=100
```

## Rollback Procedures

### 1. Quick Rollback
```bash
# Rollback processing engine
kubectl rollout undo deployment/terrafloww-processing-engine -n terrafloww-platform

# Rollback Ray cluster (requires image tag)
sed -i 's|ray-custom:.*|ray-custom:PREVIOUS_TAG|g' infra/k8s/ray-cluster.yaml
kubectl apply -f infra/k8s/ray-cluster.yaml
```

### 2. Full Rollback
1. Identify previous working image tags from Git history
2. Update deployment files with previous tags
3. Redeploy using deployment scripts


## Security Considerations

1. **Internal Communication**: All Ray-to-Flight communication uses internal Kubernetes DNS
2. **Image Security**: All images built from trusted base images with security updates
3. **Secrets Management**: Sensitive data stored in Kubernetes secrets
4. **Network Policies**: Consider implementing network policies for additional security

---

**Next**: See [README.md](README.md) for development setup and architecture details.
