#!/bin/bash
# SPDX-FileCopyrightText: Terrafloww Labs, 2025

# Build script for custom Ray Docker image with platform dependencies
# Usage: ./build-ray-image.sh [registry/repo:tag]

set -e

# Default image name
DEFAULT_IMAGE="terrafloww/ray-custom:2.47.1"
IMAGE_NAME="${1:-$DEFAULT_IMAGE}"

echo "Building custom Ray image: $IMAGE_NAME"

# Change to repository root
cd "$(dirname "$0")/../.."

# Build the Docker image
echo "Building Docker image..."
docker build \
    -f infra/k8s/ray-custom.Dockerfile \
    -t "$IMAGE_NAME" \
    .

echo "Image built successfully: $IMAGE_NAME"

# Check image size
echo "Image size:"
docker images "$IMAGE_NAME" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}"

# Optional: Push to registry if requested
if [[ "${PUSH_IMAGE:-false}" == "true" ]]; then
    echo "Pushing image to registry..."
    docker push "$IMAGE_NAME"
    echo "Image pushed successfully!"
fi

echo "Build complete!"
echo "To push the image, run: PUSH_IMAGE=true $0 $IMAGE_NAME"
echo "To use in Kubernetes, update your RayCluster manifest to use: $IMAGE_NAME"
