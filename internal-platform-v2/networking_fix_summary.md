# Ray Worker Flight Server Networking Fix

## Problem Summary
Ray workers were experiencing connection drops when uploading data to the Flight server due to network "hairpinning" - workers were connecting to the Flight server via external LoadBalancer IPs instead of using efficient internal Kubernetes service DNS.

## Root Cause
The workers were using `FLIGHT_HOST` and `FLIGHT_PORT` environment variables that pointed to external LoadBalancer IPs (`*************:50052`), causing the network path:
```
Ray Worker Pod → K8s Node → Egress Gateway → Internet → DigitalOcean Load Balancer → Ingress → K8s Service → Processing Engine Pod
```

This inefficient path introduced connection timeouts and drops during gRPC `do_put` operations.

## Solution Implemented
Changed the architecture to use internal Kubernetes service DNS for direct pod-to-pod communication:
```
Ray Worker Pod → K8s Service (ClusterIP) → Processing Engine Pod
```

## Changes Made

### 1. Driver Changes (`driver.py`)
- Added logic to get internal Flight address from `FLIGHT_INTERNAL_SVC_ADDRESS` environment variable
- Modified `execute_workflow` to pass the internal address to <PERSON> workers
- Updated `_signal_completion_to_flight_server` to use the same internal address

### 2. Worker Changes (`worker.py`)
- Modified `_upload_batch_to_flight_server` to accept `flight_address` parameter
- Updated `process_batch_on_worker` function signature to accept `flight_address`
- Removed dependency on `FLIGHT_HOST`/`FLIGHT_PORT` environment variables in workers

### 3. Kubernetes Configuration (`processing-engine-deployment.yaml`)
- Added `FLIGHT_INTERNAL_SVC_ADDRESS` environment variable:
  ```yaml
  - name: FLIGHT_INTERNAL_SVC_ADDRESS
    value: "terrafloww-processing-engine-svc.terrafloww-platform.svc.cluster.local:50052"
  ```

## Benefits
1. **Efficiency**: Direct internal cluster communication
2. **Reliability**: Eliminates external load balancer as point of failure
3. **Security**: Internal traffic not exposed to public internet
4. **Performance**: Reduced latency and improved throughput
5. **Correctness**: Standard Kubernetes service-to-service communication pattern

## Testing Required
1. Deploy the updated processing engine with new environment variable
2. Run Ray worker tasks that upload to Flight server
3. Verify no connection drops or timeout errors
4. Confirm data integrity and successful uploads

## Files Modified
- `internal-platform-v2/libs/tfw_engine_core/src/terrafloww/engine_core/runtime_ray/driver.py`
- `internal-platform-v2/libs/tfw_engine_core/src/terrafloww/engine_core/runtime_ray/worker.py`
- `internal-platform-v2/infra/k8s/processing-engine-deployment.yaml`

## Next Steps
1. Build and deploy the updated processing engine
2. Test with existing Ray workflows
3. Monitor logs for successful internal connections
4. Verify end-to-end data flow works correctly
